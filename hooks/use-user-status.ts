"use client"

import { useCallback, useEffect, useState } from "react"
import { getCurrentUser, getCurrentUserFromAPI, refreshUserStatus } from "@/utils/auth"

/**
 * 用户状态管理Hook
 * 提供实时的用户状态获取和刷新功能
 */
export function useUserStatus() {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<number>(0)

  // 从localStorage获取用户信息
  const getUserFromLocal = useCallback(() => {
    return getCurrentUser()
  }, [])

  // 从API获取最新用户信息
  const getUserFromAPI = useCallback(async () => {
    try {
      setIsLoading(true)
      const apiUser = await getCurrentUserFromAPI()
      setUser(apiUser)
      setLastRefresh(Date.now())
      return apiUser
    } catch (error) {
      console.error("Failed to fetch user from API:", error)
      // 如果API调用失败，使用localStorage中的数据
      const localUser = getUserFromLocal()
      setUser(localUser)
      return localUser
    } finally {
      setIsLoading(false)
    }
  }, [getUserFromLocal])

  // 刷新用户状态
  const refresh = useCallback(async () => {
    await refreshUserStatus()
    return getUserFromAPI()
  }, [getUserFromAPI])

  // 检查用户是否有特定权限
  const hasPermission = useCallback(
    (permission: "paid" | "email_verified" | "admin") => {
      if (!user) return false

      switch (permission) {
        case "paid":
          return user.paid === true
        case "email_verified":
          return user.email_verified === true
        case "admin":
          return user.role === "admin"
        default:
          return false
      }
    },
    [user]
  )

  // 获取用户状态摘要
  const getStatusSummary = useCallback(() => {
    if (!user) {
      return {
        isLoggedIn: false,
        isEmailVerified: false,
        isPaid: false,
        isAdmin: false,
        role: "guest",
      }
    }

    return {
      isLoggedIn: true,
      isEmailVerified: user.email_verified === true,
      isPaid: user.paid === true,
      isAdmin: user.role === "admin",
      role: user.role || "user",
    }
  }, [user])

  // 初始化时获取用户信息
  useEffect(() => {
    const initUser = async () => {
      // 先从localStorage获取用户信息，快速显示
      const localUser = getUserFromLocal()
      setUser(localUser)
      setIsLoading(false)

      // 如果有用户信息，再从API获取最新状态
      if (localUser) {
        try {
          await getUserFromAPI()
        } catch (error) {
          // 忽略API错误，继续使用localStorage中的数据
        }
      }
    }

    initUser()
  }, [getUserFromLocal, getUserFromAPI])

  // 监听localStorage变化
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "user") {
        const localUser = getUserFromLocal()
        setUser(localUser)
      }
    }

    window.addEventListener("storage", handleStorageChange)
    return () => window.removeEventListener("storage", handleStorageChange)
  }, [getUserFromLocal])

  return {
    user,
    isLoading,
    lastRefresh,
    refresh,
    getUserFromAPI,
    hasPermission,
    getStatusSummary,
  }
}

/**
 * 用户权限检查Hook
 * 专门用于权限验证的轻量级Hook
 */
export function useUserPermissions() {
  const { user, hasPermission, getStatusSummary } = useUserStatus()

  return {
    user,
    hasPermission,
    getStatusSummary,
    // 便捷的权限检查方法
    isPaid: hasPermission("paid"),
    isEmailVerified: hasPermission("email_verified"),
    isAdmin: hasPermission("admin"),
    isLoggedIn: !!user,
  }
}

/**
 * 实时用户状态Hook
 * 自动定期刷新用户状态，适用于需要实时数据的场景
 */
export function useRealtimeUserStatus(refreshInterval: number = 5 * 60 * 1000) {
  // 默认5分钟刷新一次
  const { user, isLoading, refresh, hasPermission, getStatusSummary } = useUserStatus()
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false)

  // 启用/禁用自动刷新
  const enableAutoRefresh = useCallback(() => setAutoRefreshEnabled(true), [])
  const disableAutoRefresh = useCallback(() => setAutoRefreshEnabled(false), [])

  // 自动刷新逻辑
  useEffect(() => {
    if (!autoRefreshEnabled || !user) return

    const interval = setInterval(() => {
      refresh().catch(console.error)
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefreshEnabled, user, refresh, refreshInterval])

  return {
    user,
    isLoading,
    refresh,
    hasPermission,
    getStatusSummary,
    autoRefreshEnabled,
    enableAutoRefresh,
    disableAutoRefresh,
  }
}
