/**
 * 高德地图工具函数
 * 提供地图相关的URL生成和功能
 */

// 地点类型定义
export interface MapLocation {
  name: string
  viewOnMapName: string
  address: string
  coordinates: { lat: number; lng: number }
  type?: "venue" | "hotel"
}

/**
 * 生成高德地图导航URL
 * @param location 地点信息
 * @returns 导航URL
 */
export function generateAmapNaviUrl(location: MapLocation): string {
  const { coordinates, viewOnMapName } = location
  // 使用高德地图的导航URL格式：amap.com/navi?dest=经度,纬度,名称
  return `https://www.amap.com/navi?dest=${coordinates.lng},${coordinates.lat},${encodeURIComponent(viewOnMapName)}`
}

/**
 * 生成高德地图显示URL（用于嵌入式地图）
 * @param location 地点信息
 * @returns 导航URL（直接显示位置，不使用搜索）
 */
export function generateAmapSearchUrl(location: MapLocation): string {
  const { viewOnMapName, coordinates } = location
  // 直接使用导航URL来显示地图，避免搜索结果错误
  return `https://www.amap.com/navi?dest=${coordinates.lng},${coordinates.lat}&destName=${encodeURIComponent(viewOnMapName)}&hideRouteIcon=1`
}

/**
 * 生成高德地图嵌入式iframe URL
 * @param location 地点信息
 * @returns 专门用于iframe嵌入的URL
 */
export function generateAmapIframeUrl(location: MapLocation): string {
  const { viewOnMapName, coordinates } = location
  // 使用高德地图的简单地图页面，更适合iframe嵌入
  const params = new URLSearchParams({
    q: viewOnMapName,
    center: `${coordinates.lng},${coordinates.lat}`,
    zoom: "15",
  })
  return `https://www.amap.com/search?${params.toString()}`
}

/**
 * 生成高德地图标记URL
 * @param location 地点信息
 * @returns 标记URL
 */
export function generateAmapMarkerUrl(location: MapLocation): string {
  const { coordinates, viewOnMapName } = location
  // 使用高德地图的URI scheme来打开地图应用
  return `https://uri.amap.com/marker?position=${coordinates.lng},${coordinates.lat}&name=${encodeURIComponent(viewOnMapName)}&src=mypage`
}

/**
 * 生成高德地图嵌入式地图URL
 * @param location 地点信息
 * @param apiKey API密钥
 * @returns 嵌入式地图URL
 */
export function generateAmapEmbedUrl(location: MapLocation, apiKey?: string): string {
  const { coordinates, viewOnMapName } = location
  const key = apiKey || process.env.NEXT_PUBLIC_AMAP_API_KEY || ""

  // 使用高德地图的JavaScript API来生成嵌入式地图
  const baseUrl = "https://webapi.amap.com/maps"
  const params = new URLSearchParams({
    v: "2.0",
    key: key,
    center: `${coordinates.lng},${coordinates.lat}`,
    zoom: "15",
    marker: `${coordinates.lng},${coordinates.lat}`,
    markerLabel: viewOnMapName,
  })

  return `${baseUrl}?${params.toString()}`
}

/**
 * 生成Google Maps URL
 * @param location 地点信息
 * @returns Google Maps URL
 */
export function generateGoogleMapsUrl(location: MapLocation): string {
  const { coordinates, name } = location
  // 使用Google Maps的搜索和标记功能
  return `https://www.google.com/maps/search/?api=1&query=${coordinates.lat},${coordinates.lng}&query_place_id=${encodeURIComponent(name)}`
}

/**
 * 获取高德地图API密钥
 * @returns API密钥
 */
export function getAmapApiKey(): string {
  return process.env.NEXT_PUBLIC_AMAP_API_KEY || ""
}
