{"$schema": "https://json.schemastore.org/tsconfig", "display": "Next.js", "compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noUncheckedIndexedAccess": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "types": ["node", "jest", "@testing-library/jest-dom"], "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "map": ["./components/auth/route-guard"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.mjs", "jest.config.js", ".next/types/**/*.ts", "eslint.config.mjs"], "exclude": ["node_modules"]}