# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*

# Next.js
.next/

# Misc
.DS_Store
*.tsbuildinfo

# Debug
# Local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Storybook
storybook-static

# IDE
.vscode
.idea
*.swp
*.swo

# OS
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# CI/CD
.github

# Logs
logs

# Runtime data
pids
*.pid
*.seed
*.pid.lock


# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history


# Yarn Integrity file
.yarn-integrity

.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

*.md### Example user template template
### Example user template

### react template
.DS_*
**/*.backup.*
**/*.back.*
bower_components

*.sublime*

psd
thumb
sketch

### WebStorm+all template
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf

# AWS User-specific
.idea/**/aws.xml

# Generated files
.idea/**/contentModel.xml

# Sensitive or high-churn files
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml

# Gradle
.idea/**/gradle.xml
.idea/**/libraries

# Gradle and Maven with auto-import
# When using Gradle or Maven with auto-import, you should exclude module files,
# since they will be recreated, and may cause churn.  Uncomment if using
# auto-import.
# .idea/artifacts
# .idea/compiler.xml
# .idea/jarRepositories.xml
# .idea/modules.xml
# .idea/*.iml
# .idea/modules
# *.iml
# *.ipr

# CMake
cmake-build-*/

# Mongo Explorer plugin
.idea/**/mongoSettings.xml

# File-based project format
*.iws

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Cursive Clojure plugin
.idea/replstate.xml

# SonarLint plugin
.idea/sonarlint/

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# Editor-based Rest Client
.idea/httpRequests
docs
# Android studio 3.1+ serialized cache file
.idea/caches/build_file_checksums.ser

### NextJS template
# dependencies
/.pnp
.pnp.js

# testing
/coverage

# next.js
/out/

# production
/build

# misc
# debug
.pnpm-debug.log*

# local env files
# typescript
next-env.d.ts

