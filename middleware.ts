/**
 * Next.js 中间件
 * 处理 favicon 重定向
 */

import { NextRequest, NextResponse } from "next/server"

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 处理 favicon.ico 请求，直接重定向到 MinIO
  if (pathname === "/favicon.ico") {
    return NextResponse.redirect(
      "https://minioapi.bugmaker.me/ifmb-2025-public/favicon.ico",
      { status: 301 } // 永久重定向
    )
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    // 只匹配 favicon.ico
    "/favicon.ico",
  ],
}
