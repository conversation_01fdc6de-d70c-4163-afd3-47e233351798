import withBundleAnalyzer from "@next/bundle-analyzer"
import { type NextConfig } from "next"

import { env } from "./env.mjs"

const config: NextConfig = {
  reactStrictMode: true,
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  rewrites: async () => [
    { source: "/healthz", destination: "/api/health" },
    { source: "/api/healthz", destination: "/api/health" },
    { source: "/health", destination: "/api/health" },
    { source: "/ping", destination: "/api/health" },
  ],
  // 重定向配置
  redirects: async () => [
    // 重定向本地 favicon.ico 到 MinIO
    {
      source: "/favicon.ico",
      destination: "https://minioapi.bugmaker.me/ifmb-2025-public/favicon.ico",
      permanent: true,
    },
  ],
  // 添加响应头来优化缓存
  headers: async () => [
    {
      source: "/favicon.ico",
      headers: [
        {
          key: "Cache-Control",
          value: "public, max-age=31536000, immutable",
        },
      ],
    },
  ],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "minioapi.bugmaker.me",
        port: "",
        pathname: "/**",
      },
    ],
  },
}

export default env.ANALYZE ? withBundleAnalyzer({ enabled: env.ANALYZE })(config) : config
