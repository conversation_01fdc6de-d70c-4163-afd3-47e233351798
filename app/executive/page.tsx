import { Metadata } from "next"
import ExecutiveClient from "@/components/executive/executive-client"

export const metadata: Metadata = {
  title: "Executive Committee | IFMB 2025",
  description: "Meet the distinguished executive committee members of the International Forum on Maize Biology 2025.",
  keywords: "IFMB executive committee, maize biology committee, corn research leaders, IFMB 2025 organizers",
}

/**
 * Executive Committee page
 */
export default function ExecutiveCommitteePage() {
  // Leadership data
  const leadership = [
    {
      role: "Chairman",
      members: [
        {
          name: "<PERSON><PERSON><PERSON>",
          title: "Ph.D, Professor",
          affiliation: "China Agricultural University, Beijing, China",
          image: "Mingliang-Xu.jpg",
        },
      ],
    },
  ]

  // Committee members
  const committeeMembers = [
    { name: "<PERSON><PERSON>", title: "Professor", affiliation: "University of Science and Technology Beijing" },
    { name: "<PERSON><PERSON><PERSON>", title: "Professor", affiliation: "Huazhong Agricultural University" },
    { name: "Hong <PERSON>", title: "Professor", affiliation: "North-East Agricultural University" },
    { name: "<PERSON><PERSON>", title: "Professor", affiliation: "Lanzhou University" },
    { name: "<PERSON><PERSON><PERSON>", title: "Professor", affiliation: "Sichuan Agricultural University" },
    { name: "<PERSON><PERSON><PERSON>", title: "Professor", affiliation: "China Agricultural University" },
    { name: "<PERSON><PERSON>", title: "Scientist", affiliation: "Yazhouwan National Laboratory" },
    { name: "Lei Liu", title: "Professor", affiliation: "Huazhong Agricultural University" },
    { name: "Jincheng Long", title: "Professor", affiliation: "Shanghai Academy of Life Sciences, CAS" },
    { name: "Feng Tian", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Guanfeng Wang", title: "Professor", affiliation: "Shandong University" },
    { name: "Guifeng Wang", title: "Professor", affiliation: "Henan Agricultural University" },
    { name: "Yingjie Xiao", title: "Professor", affiliation: "Huazhong Agricultural University" },
    { name: "Qin Yang", title: "Professor", affiliation: "Northwest A&F University" },
    { name: "Han Zhao", title: "Scientist", affiliation: "Jiangsu Academy of Agricultural Sciences" },
    { name: "Shutu Xu", title: "Associate Professor", affiliation: "Huazhong Agricultural University" },
  ]

  // 按名称（A-Z）排序
  const sortedCommitteeMembers = [...committeeMembers].sort((a, b) => a.name.localeCompare(b.name, "en"))

  return (
    <ExecutiveClient
      leadership={leadership}
      committeeMembers={sortedCommitteeMembers}
      conferenceInfo={{
        title: "International Forum on Maize Biology",
        shortTitle: "IFMB 2025",
        dates: "October 16-20, 2025",
        location: "Huazhong Agricultural University, Wuhan, China",
      }}
    />
  )
}
