import { Metadata } from "next"
import { Suspense } from "react"
import VerifyEmail from "@/components/auth/verify-email"

export const metadata: Metadata = {
  title: "Verify Email | IFMB 2025",
  description: "Verify your email address for the International Forum on Maize Biology 2025",
}

// Loading component for Suspense
function VerifyEmailLoading() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-green-600"></div>
        <p className="text-gray-600">Loading verification page...</p>
      </div>
    </div>
  )
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={<VerifyEmailLoading />}>
      <VerifyEmail />
    </Suspense>
  )
}
