import { Metadata } from "next"
import OrganizationClient from "@/components/organization/organization-client"

export const metadata: Metadata = {
  title: "Organization Committees | IFMB 2025",
  description:
    "Meet the organization committees of the International Forum on Maize Biology 2025, including chairmen, secretary-general, and committee members.",
  keywords:
    "IFMB committees, maize biology organizers, IFMB 2025 chairmen, corn research committee, maize biology leadership",
}

/**
 * Organization Committees page for IFMB 2025
 */
export default function OrganizationCommitteesPage() {
  // Chairmen data
  const chairmen = [
    {
      name: "<PERSON>",
      title: "Ph.D, Professor",
      affiliation: "University of California, Davis, USA",
      photo: "<PERSON><PERSON>-<PERSON>barra.jpg",
    },
    {
      name: "<PERSON>",
      title: "Ph.D, Professor",
      affiliation: "Cold Spring Harbor Laboratory, Cold Spring Harbor, USA",
      photo: "<PERSON>.jpg",
    },
  ]

  // Secretary-General data
  const secretaryGeneral = {
    name: "<PERSON><PERSON><PERSON>",
    title: "Ph.D, Professor",
    affiliation: "Huazhong Agricultural University, Wuhan, China",
    photo: "Ming<PERSON>u-Dai.jpg",
  }

  // International and journal committee members
  const internationalMembers = [
    { name: "<PERSON>", title: "Senior Researcher(DR1)", affiliation: "INRAE, France" },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      title: "Professor",
      affiliation: "<PERSON>ck Institute of Molecular Plant Physiology (MPI-MP), Germany",
    },
    { name: "<PERSON> Gallavotti", title: "Professor", affiliation: "Rutgers University, USA" },
    { name: "Blake Meyers", title: "Professor", affiliation: "Member of NAS, Director, Genome Center, UC Davis" },
    // { name: "Filipe Borges", title: "Research Scientist", affiliation: "INRAE Centre Ile de France - Versailles-Grignon" },
    { name: "Hilde Nelissen", title: "Scientist", affiliation: "VIB, Belgium" },
    {
      name: "Kelly Swarts",
      title: "Scientist",
      affiliation: "Gregor Mendel Institute of Molecular Plant Biology (GMI), Austria",
    },
    { name: "Madelaine Bartlett", title: "Professor", affiliation: "University of Massachusetts, Amherst" },
    { name: "Magnus Nordborg", title: "Scientist", affiliation: "GMI, Austria" },
    { name: "Matthew Hufford", title: "Professor", affiliation: "Iowa State University, USA" },
    { name: "Ruben Rellan-Alvarez", title: "Professor", affiliation: "North Carolina State University, USA" },
    { name: "Sarah Hearne", title: "Assistant Professor", affiliation: "CIMMYT, Mexico" },
    { name: "Thomas Lübberstedt", title: "Professor", affiliation: "Iowa State University, USA" },
    { name: "Uta Pazkowski", title: "Professor", affiliation: "University of Cambridge, UK" },
    { name: "Xuecai Zhang", title: "Scientist", affiliation: "CIMMYT, Mexico" },
    { name: "Xiaofeng Cui", title: "Executive Chief Editor", affiliation: "Molecular Plant" },
    { name: "Judith Nicholson", title: "Editor", affiliation: "Cell Genomics" },
    { name: "Jun Lv", title: "Senior Editor", affiliation: "Nature Plants" },
    { name: "Wenjing She", title: "Senior Editor", affiliation: "Genome Biology" },
    { name: "Yang Yang", title: "Senior Editor", affiliation: "Cell" },
    { name: "Jinliang Yang", title: "Professor", affiliation: "University of Nebraska-Lincoln" },
    { name: "Wenjia Wang", title: "Senior Editor", affiliation: "Nature Communications" },
  ]

  // Chinese committee members
  const chineseMembers = [
    { name: "Saihua Chen", title: "Professor", affiliation: "Yangzhou University" },
    { name: "Gang Li", title: "Professor", affiliation: "Shangdong Agricultural University" },
    { name: "Zhubing Hu", title: "Professor", affiliation: "Henan University" },
    { name: "Jun Zheng", title: "Scienstist", affiliation: "Chinese Academy of Agricultural Sciences" },
    { name: "Yun Xiang", title: "Professor", affiliation: "Lanzhou University" },
    { name: "Liuji Wu", title: "Professor", affiliation: "Henan Agricultural University" },
    { name: "Peijin Li", title: "Professor", affiliation: "Anhui Agricultural Universtiy" },
    { name: "Caifu Jiang", title: "Professor", affiliation: "China Agricultural Universtiy" },
    { name: "Lei Liu", title: "Professor", affiliation: "Huazhong Agricultural Universtiy" },
    { name: "Xiangguo Liu", title: "Scientist", affiliation: "Jilin Academy of Agricultural Sciences" },
    { name: "Yingjie Xiao", title: "Professor", affiliation: "Huazhong Agricultural Universtiy" },
    { name: "Shibin Gao", title: "Professor", affiliation: "Sichuan Agricultural University" },
    { name: "Xiquan Gao", title: "Professor", affiliation: "Nanjing Agricultural University" },
    { name: "Mingyue Gou", title: "Professor", affiliation: "Henan Agricultural University" },
    { name: "Siyi Guo", title: "Professor", affiliation: "Henan University" },
    { name: "Tingting Guo", title: "Professor", affiliation: "Huazhong Agricultural University" },
    { name: "Yan He", title: "Scientist", affiliation: "Institute of Genetics and Developmental Biology" },
    { name: "Yongcai Huang", title: "Professor", affiliation: "Sichuan Agricultural University" },
    { name: "Haiyang Jiang", title: "Professor", affiliation: "Anhui Agricultural University" },
    { name: "Jinsheng Lai", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Zhibing Lai", title: "Professor", affiliation: "Huazhong Agricultural University" },
    { name: "Chunhui Li", title: "Scientist", affiliation: "Institute of Crop Science, CAAS" },
    { name: "Lin Li", title: "Professor", affiliation: "Huazhong Agricultural University" },
    { name: "Pinghua Li", title: "Professor", affiliation: "Shandong Agricultural University" },
    { name: "Qing Li", title: "Professor", affiliation: "Huazhong Agricultural University" },
    { name: "Wenxue Li", title: "Scientist", affiliation: "Institute of Crop Science, CAAS" },
    { name: "Zhongwei Lin", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Haijun Liu", title: "Scientist", affiliation: "Yazhouwan National Laboratory" },
    { name: "Yanli Lu", title: "Professor", affiliation: "Sichuan Agricultural University" },
    { name: "Li Pu", title: "Scientist", affiliation: "Institute of Biotechnology, CAAS" },
    { name: "Feng Qin", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Baoxing Song", title: "Scientist", affiliation: "Institute of Modern Agriculture, Peking University" },
    { name: "Yaou Shen", title: "Professor", affiliation: "Sichuan Agricultural University" },
    { name: "Weibin Song", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Baocai Tan", title: "Professor", affiliation: "Shandong University" },
    { name: "Jihua Tang", title: "Professor", affiliation: "Henan Agricultural University" },
    { name: "Feng Tian", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Xiangyuan Wan", title: "Professor", affiliation: "University of Science and Technology Beijing" },
    { name: "Baobao Wang", title: "Scientist", affiliation: "Institute of Biotechnology, CAAS" },
    { name: "Guanfeng Wang", title: "Professor", affiliation: "Shandong University" },
    { name: "Guifeng Wang", title: "Professor", affiliation: "Henan Agricultural University" },
    { name: "Hai Wang", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Haiyang Wang", title: "Professor", affiliation: "Yazhouwan National Laboratory" },
    { name: "Zhenhua Wang", title: "Professor", affiliation: "North-East Agricultural University" },
    { name: "Yongrui Wu", title: "Scientist", affiliation: "Institute of Plant Physiology and Ecology, CAS" },
    { name: "Chuanxiao Xie", title: "Scientist", affiliation: "Institute of Biotechnology, CAAS" },
    { name: "Chenwu Xu", title: "Professor", affiliation: "Yangzhou University" },
    { name: "Fang Xu", title: "Professor", affiliation: "Shandong University" },
    { name: "Mingliang Xu", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Fang Yang", title: "Professor", affiliation: "Sun Yat-sen University" },
    { name: "Jun Yang", title: "Professor", affiliation: "Anhui Agricultural University" },
    { name: "Ning Yang", title: "Professor", affiliation: "Huazhong Agricultural University" },
    { name: "Qin Yang", title: "Professor", affiliation: "Northwest A&F University" },
    { name: "Xiaohong Yang", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Han Zhao", title: "Scientist", affiliation: "Institute of Germplasm Resources and Biotechnology, JAAS" },
    { name: "Xiangyu Zhao", title: "Professor", affiliation: "Shandong Agricultural University" },
    { name: "Zhaobin Dong", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Shuhua Yang", title: "Professor", affiliation: "China Agricultural University" },
    { name: "Xinhai Li", title: "Professor", affiliation: "Chinese Academy of Agricultural Sciences" },
  ]

  // Conference basic info
  const conferenceInfo = {
    title: "International Forum on Maize Biology",
    shortTitle: "IFMB 2025",
    dates: "October 16-20, 2025",
    location: "Huazhong Agricultural University, Wuhan, China",
  }

  const getLastName = (fullName: string) => {
    const nameParts = fullName.split(" ")
    return nameParts[nameParts.length - 1]
  }

  // 按姓氏（A-Z）排序
  const sortedChairmen = [...chairmen].sort((a, b) => {
    const lastNameA = getLastName(a.name) || ""
    const lastNameB = getLastName(b.name) || ""
    return lastNameA.localeCompare(lastNameB)
  })
  const sortedInternationalMembers = [...internationalMembers].sort((a, b) => {
    const lastNameA = getLastName(a.name) || ""
    const lastNameB = getLastName(b.name) || ""
    return lastNameA.localeCompare(lastNameB)
  })
  const sortedChineseMembers = [...chineseMembers].sort((a, b) => {
    const lastNameA = getLastName(a.name) || ""
    const lastNameB = getLastName(b.name) || ""
    return lastNameA.localeCompare(lastNameB)
  })

  return (
    <>
      <OrganizationClient
        chairmen={sortedChairmen}
        secretaryGeneral={secretaryGeneral}
        internationalMembers={sortedInternationalMembers}
        chineseMembers={sortedChineseMembers}
        conferenceInfo={conferenceInfo}
      />
    </>
  )
}
