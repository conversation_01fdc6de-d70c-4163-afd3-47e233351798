import { Metadata } from "next"
import ReportersClient from "@/components/reporters/reporters-client"

export const metadata: Metadata = {
  title: "Reporters | IFMB 2025",
  description: "Meet the distinguished reporters and speakers of the International Forum on Maize Biology 2025.",
  keywords: "IFMB reporters, maize biology speakers, corn research presenters, IFMB 2025 speakers",
}

/**
 * Reporters page for IFMB 2025
 */
export default function ReportersPage() {
  // International reporters data
  const internationalReporters = [
    {
      name: "<PERSON>",
      title: "Professor",
      affiliation: "UC Davis, USA",
      region: "North America",
      field: "Plant sRNA, PC former chief editor",
      photo: "<PERSON><PERSON><PERSON>.jpg",
    },
    {
      name: "<PERSON>",
      title: "Professor",
      affiliation: "UC Davis, USA",
      region: "North America",
      field: "Maize Evolutionary Genetics",
      photo: "Jeffrey<PERSON>Ross-<PERSON>barra.jpg",
    },
    {
      name: "<PERSON>",
      title: "Professor",
      affiliation: "Cold Spring Habor laboratory, USA",
      region: "North America",
      field: "Maize Development",
      photo: "<PERSON><PERSON><PERSON>.jpg",
    },
    {
      name: "<PERSON>",
      title: "<PERSON>",
      affiliation: "<PERSON> Institute of Molecular Plant Biology, USA",
      region: "North America",
      field: "Maize Quantitative Genetics/Genomics",
      photo: "Kelly-Swarts.jpg",
    },
    {
      name: "<PERSON>fford",
      title: "Professor",
      affiliation: "Iowa State University, USA",
      region: "North America",
      field: "Maize Evolutionary Genetics",
      photo: "Matthew-Hufford.jpg",
    },
    {
      name: "Hilde Nelissen",
      title: "Scientist",
      affiliation: "VIB, Belgium",
      region: "Europe",
      field: "Maize development regulation",
      photo: "Hilde-Nelissen.jpg",
    },
    {
      name: "Ruben Rellan-Alvarez",
      title: "Professor",
      affiliation: "North Carolina State University, USA",
      region: "North America",
      field: "Maize abiotic stress adaptation",
      photo: "Ruben-Rellan-Alvarez.jpg",
    },
    {
      name: "Alisdair R Fernie",
      title: "Professor",
      affiliation: "MPI-MP, Germany",
      region: "Europe",
      field: "Plant Metabolite biology",
      photo: "Alisdair-R-Fernie.jpg",
    },
    {
      name: "Sarah Hearne",
      title: "Assistant Professor",
      affiliation: "Mexico",
      region: "North America",
      field: "Maize germplasm resources and breeding",
      photo: "Sarah-Hearne.jpg",
    },
    {
      name: "Magnus Nordborg",
      title: "Scientist",
      affiliation: "Gregor Mendel Institute of Molecular Plant Biology, Austria",
      region: "Europe",
      field: "Plant Genetics/Genomics",
      photo: "Magnus-Nordborg.jpg",
    },
    {
      name: "Madelaine Bartlett",
      title: "Professor",
      affiliation: "University of Massachusetts, Amherst",
      region: "Europe",
      field: "Plant Developmental Evolution",
      photo: "Madelaine-Bartlett.jpg",
    },
    {
      name: "Alain Charcosset",
      title: "Senior Researcher(DR1)",
      affiliation: "INRAE, France",
      region: "Europe",
      field: "Quantitative genetics and plant breeding methodology",
      photo: "Alain-Charcosset.jpg",
    },
    {
      name: "Andrea Gallavotti",
      title: "Professor",
      affiliation: "Rutgers University, USA",
      region: "North America",
      field: "Maize developmental biology",
      photo: "Andrea-Gallavotti.jpg",
    },
    {
      name: "Thomas Lübberstedt",
      title: "Professor",
      affiliation: "Iowa State University, USA",
      region: "North America",
      field: "Maize breeding and genetics",
      photo: "Thomas-Lueberstedt.jpg",
    },
    {
      name: "Filipe Borges",
      title: "Research Scientist",
      affiliation: "INRAE Centre Ile de France - Versailles-Grignon",
      region: "Europe",
      field: "Epigenetics, Reproduction and Transposable Elements",
      photo: "Filipe-Borges.jpg",
    },
    {
      name: "Jinliang Yang",
      title: "Professor",
      affiliation: "University of Nebraska-Lincoln, USA",
      region: "North America",
      field: "Maize genetics",
      photo: "Jingliang-Yang.jpg",
    },
    {
      name: "Michelle Sitzer",
      title: "Doctor",
      affiliation: "Cornell University, USA",
      region: "North America",
      field: "Maize Evolutionary Genetics",
      photo: "Michelle-Sitzer.jpg",
    },
    {
      name: "Judith (Jude) Nicholson",
      title: "Editor",
      affiliation: "Cell Genomics, UK",
      region: "Europe",
      field: "editor",
      photo: "Judith-(Jude)-Nicholson.jpg",
    },
  ]

  // Chinese reporters data
  const chineseReporters = [
    {
      name: "Jiankang Zhu",
      title: "Professor",
      affiliation: "Southern University of Science and Technology, USA",
      region: "China",
      field: "Plant stress biology",
      photo: "Jiankang-Zhu.jpg",
    },
    {
      name: "Xiaofeng Cui",
      title: "Executive Chief Editor",
      affiliation: "Molecular Plant, China",
      region: "China",
      field: "MP editor",
      photo: "Xiaofeng-Cui.jpg",
    },
    {
      name: "Jun Lv",
      title: "Senior Editor",
      affiliation: "Nature Plants, China",
      region: "China",
      field: "Nature Plant editor",
      photo: "Jun-Lv.jpg",
    },
    {
      name: "Wenjia Wang",
      title: "Senior Editor",
      affiliation: "Nature Communications",
      region: "China",
      // field: "GB editor",
      photo: "Wenjia-Wang.jpg",
    },
    {
      name: "Wenjing She",
      title: "Senior Editor",
      affiliation: "Genome Biology, China",
      region: "China",
      field: "GB editor",
      photo: "Wenjing-She.jpg",
    },
    {
      name: "Qifa Zhang",
      title: "Professor",
      affiliation: "Huazhong Agricultural University",
      region: "China",
      photo: "qifa-zhang.jpg",
    },
    {
      name: "Xinhai Li",
      title: "Professor",
      affiliation: "Science and Technology Management Bureau, CAAS",
      region: "China",
      photo: "xinhai-li.jpg",
    },
    {
      name: "Jinsheng Lai",
      title: "Professor",
      affiliation: "China Agricultural University",
      region: "China",
      photo: "jinsheng-lai.jpg",
    },
    {
      name: "Baocai Tan",
      title: "Professor",
      affiliation: "Shandong University",
      region: "China",
      photo: "baocai-tan.jpg",
    },
    {
      name: "Haiyang Wang",
      title: "Professor",
      affiliation: "Yazhouwan National Laboratory",
      region: "China",
      photo: "haiyang-wang.jpg",
    },
    {
      name: "Yongrui Wu",
      title: "Scientist",
      affiliation: "Institute of Plant Physiology and Ecology, CAS",
      region: "China",
      photo: "yongrui-wu.jpg",
    },
    {
      name: "Xiangyuan Wan",
      title: "Professor",
      affiliation: "University of Science and Technology Beijing",
      region: "China",
      photo: "xiangyuan-wan.jpg",
    },
    {
      name: "Guifeng Wang",
      title: "Professor",
      affiliation: "Henan Agricultural University",
      region: "China",
      photo: "guifeng-wang.jpg",
    },
    {
      name: "Guangchao Sun",
      title: "Professor",
      affiliation: "Sichuan Agricultural University",
      region: "China",
      photo: "guangchao-sun.jpg",
    },
    {
      name: "Wenxue Li",
      title: "Scientist",
      affiliation: "Institute of Crop Science, CAAS",
      region: "China",
      photo: "wenxue-li.jpg",
    },
    {
      name: "Peijin Li",
      title: "Professor",
      affiliation: "Anhui Agricultural University",
      region: "China",
      photo: "peijin-li.jpg",
    },
    {
      name: "Xiangyu Zhao",
      title: "Professor",
      affiliation: "Shandong Agricultural University",
      region: "China",
      photo: "xiangyu-zhao.jpg",
    },
    {
      name: "Fang Yang",
      title: "Professor",
      affiliation: "Sun Yat-sen University",
      region: "China",
      photo: "fang-yang.jpg",
    },
    {
      name: "Qin Yang",
      title: "Professor",
      affiliation: "Northwest A&F University",
      region: "China",
      photo: "qin-yang.jpg",
    },
  ]

  // 提取姓氏的函数
  const getLastName = (name: string) => {
    // 对于英文名字，取最后一个单词作为姓氏
    // 对于中文名字，取第一个字符作为姓氏
    const parts = name.trim().split(/\s+/)
    if (parts.length === 1) {
      // 单个词，可能是中文名字，取第一个字符
      return parts[0]?.charAt(0) || ""
    }
    // 多个词，取最后一个词作为姓氏
    return parts[parts.length - 1] || ""
  }

  // 按姓氏（lastname）排序
  const sortedInternationalReporters = [...internationalReporters].sort((a, b) =>
    getLastName(a.name).localeCompare(getLastName(b.name), "en")
  )
  const sortedChineseReporters = [...chineseReporters].sort((a, b) =>
    getLastName(a.name).localeCompare(getLastName(b.name), "en")
  )

  // Combine all reporters
  const allReporters = [...sortedInternationalReporters, ...sortedChineseReporters]

  // 提取工作单位地区的函数
  const getWorkplaceRegion = (affiliation: string) => {
    // 从工作单位中提取地区信息
    const lowerAffiliation = affiliation.toLowerCase()

    // 中国地区
    if (
      lowerAffiliation.includes("china") ||
      lowerAffiliation.includes("beijing") ||
      lowerAffiliation.includes("shanghai") ||
      lowerAffiliation.includes("guangzhou") ||
      lowerAffiliation.includes("shenzhen") ||
      lowerAffiliation.includes("wuhan") ||
      lowerAffiliation.includes("chengdu") ||
      lowerAffiliation.includes("xian") ||
      lowerAffiliation.includes("nanjing") ||
      lowerAffiliation.includes("hangzhou") ||
      lowerAffiliation.includes("tianjin") ||
      lowerAffiliation.includes("qingdao") ||
      lowerAffiliation.includes("dalian") ||
      lowerAffiliation.includes("shenyang") ||
      lowerAffiliation.includes("harbin") ||
      lowerAffiliation.includes("changchun") ||
      lowerAffiliation.includes("hefei") ||
      lowerAffiliation.includes("fuzhou") ||
      lowerAffiliation.includes("xiamen") ||
      lowerAffiliation.includes("nanchang") ||
      lowerAffiliation.includes("jinan") ||
      lowerAffiliation.includes("zhengzhou") ||
      lowerAffiliation.includes("changsha") ||
      lowerAffiliation.includes("nanning") ||
      lowerAffiliation.includes("haikou") ||
      lowerAffiliation.includes("guiyang") ||
      lowerAffiliation.includes("kunming") ||
      lowerAffiliation.includes("lhasa") ||
      lowerAffiliation.includes("lanzhou") ||
      lowerAffiliation.includes("xining") ||
      lowerAffiliation.includes("yinchuan") ||
      lowerAffiliation.includes("urumqi") ||
      lowerAffiliation.includes("hohhot") ||
      lowerAffiliation.includes("huazhong") ||
      (lowerAffiliation.includes("agricultural university") &&
        !lowerAffiliation.includes("usa") &&
        !lowerAffiliation.includes("uk")) ||
      lowerAffiliation.includes("caas") ||
      lowerAffiliation.includes("cas")
    ) {
      return "China"
    }

    // 美国地区
    if (
      lowerAffiliation.includes("usa") ||
      lowerAffiliation.includes("united states") ||
      lowerAffiliation.includes("california") ||
      lowerAffiliation.includes("texas") ||
      lowerAffiliation.includes("new york") ||
      lowerAffiliation.includes("florida") ||
      lowerAffiliation.includes("illinois") ||
      lowerAffiliation.includes("pennsylvania") ||
      lowerAffiliation.includes("ohio") ||
      lowerAffiliation.includes("georgia") ||
      lowerAffiliation.includes("north carolina") ||
      lowerAffiliation.includes("michigan") ||
      lowerAffiliation.includes("new jersey") ||
      lowerAffiliation.includes("virginia") ||
      lowerAffiliation.includes("washington") ||
      lowerAffiliation.includes("arizona") ||
      lowerAffiliation.includes("massachusetts") ||
      lowerAffiliation.includes("tennessee") ||
      lowerAffiliation.includes("indiana") ||
      lowerAffiliation.includes("missouri") ||
      lowerAffiliation.includes("maryland") ||
      lowerAffiliation.includes("wisconsin") ||
      lowerAffiliation.includes("colorado") ||
      lowerAffiliation.includes("minnesota") ||
      lowerAffiliation.includes("south carolina") ||
      lowerAffiliation.includes("alabama") ||
      lowerAffiliation.includes("louisiana") ||
      lowerAffiliation.includes("kentucky") ||
      lowerAffiliation.includes("oregon") ||
      lowerAffiliation.includes("oklahoma") ||
      lowerAffiliation.includes("connecticut") ||
      lowerAffiliation.includes("utah") ||
      lowerAffiliation.includes("iowa") ||
      lowerAffiliation.includes("nevada") ||
      lowerAffiliation.includes("arkansas") ||
      lowerAffiliation.includes("mississippi") ||
      lowerAffiliation.includes("kansas") ||
      lowerAffiliation.includes("new mexico") ||
      lowerAffiliation.includes("nebraska") ||
      lowerAffiliation.includes("west virginia") ||
      lowerAffiliation.includes("idaho") ||
      lowerAffiliation.includes("hawaii") ||
      lowerAffiliation.includes("new hampshire") ||
      lowerAffiliation.includes("maine") ||
      lowerAffiliation.includes("montana") ||
      lowerAffiliation.includes("rhode island") ||
      lowerAffiliation.includes("delaware") ||
      lowerAffiliation.includes("south dakota") ||
      lowerAffiliation.includes("north dakota") ||
      lowerAffiliation.includes("alaska") ||
      lowerAffiliation.includes("vermont") ||
      lowerAffiliation.includes("wyoming") ||
      lowerAffiliation.includes("uc davis") ||
      lowerAffiliation.includes("cold spring") ||
      lowerAffiliation.includes("iowa state") ||
      lowerAffiliation.includes("rutgers") ||
      lowerAffiliation.includes("cornell") ||
      lowerAffiliation.includes("nebraska-lincoln")
    ) {
      return "North America"
    }

    // 欧洲地区
    if (
      lowerAffiliation.includes("uk") ||
      lowerAffiliation.includes("united kingdom") ||
      lowerAffiliation.includes("england") ||
      lowerAffiliation.includes("scotland") ||
      lowerAffiliation.includes("wales") ||
      lowerAffiliation.includes("cambridge") ||
      lowerAffiliation.includes("germany") ||
      lowerAffiliation.includes("france") ||
      lowerAffiliation.includes("italy") ||
      lowerAffiliation.includes("spain") ||
      lowerAffiliation.includes("netherlands") ||
      lowerAffiliation.includes("belgium") ||
      lowerAffiliation.includes("austria") ||
      lowerAffiliation.includes("switzerland") ||
      lowerAffiliation.includes("sweden") ||
      lowerAffiliation.includes("norway") ||
      lowerAffiliation.includes("denmark") ||
      lowerAffiliation.includes("finland") ||
      lowerAffiliation.includes("poland") ||
      lowerAffiliation.includes("czech") ||
      lowerAffiliation.includes("hungary") ||
      lowerAffiliation.includes("romania") ||
      lowerAffiliation.includes("bulgaria") ||
      lowerAffiliation.includes("croatia") ||
      lowerAffiliation.includes("slovenia") ||
      lowerAffiliation.includes("slovakia") ||
      lowerAffiliation.includes("estonia") ||
      lowerAffiliation.includes("latvia") ||
      lowerAffiliation.includes("lithuania") ||
      lowerAffiliation.includes("portugal") ||
      lowerAffiliation.includes("greece") ||
      lowerAffiliation.includes("ireland") ||
      lowerAffiliation.includes("vib") ||
      lowerAffiliation.includes("mpi-mp") ||
      lowerAffiliation.includes("cimmyt") ||
      lowerAffiliation.includes("gregor mendel") ||
      lowerAffiliation.includes("inrae")
    ) {
      return "Europe"
    }

    // 其他地区
    return "Other"
  }

  // 按工作单位地区分组
  const reportersByWorkplaceRegion = allReporters.reduce(
    (acc, reporter) => {
      const region = getWorkplaceRegion(reporter.affiliation)
      if (!acc[region]) {
        acc[region] = []
      }
      acc[region].push(reporter)
      return acc
    },
    {} as Record<string, typeof allReporters>
  )

  // Statistics
  const statistics = {
    totalReporters: allReporters.length,
    countries: new Set(allReporters.map((r) => r.affiliation.split(", ").pop() || "")).size,
    institutions: new Set(allReporters.map((r) => r.affiliation.split(", ")[0])).size,
    regions: {
      europe: sortedInternationalReporters.filter((r) => r.region === "Europe").length,
      northAmerica: sortedInternationalReporters.filter((r) => r.region === "North America").length,
      china: sortedChineseReporters.length,
      academic:
        sortedInternationalReporters.filter((r) => r.title.includes("Professor")).length +
        sortedChineseReporters.filter((r) => r.title.includes("Professor")).length,
      scientific:
        sortedInternationalReporters.filter((r) => r.title.includes("Scientist")).length +
        sortedChineseReporters.filter((r) => r.title.includes("Scientist")).length,
    },
    workplaceRegions: reportersByWorkplaceRegion,
  }

  return (
    <ReportersClient
      internationalReporters={sortedInternationalReporters}
      chineseReporters={sortedChineseReporters}
      statistics={statistics}
    />
  )
}
