import { NextRequest, NextResponse } from "next/server"
import { verifyAuthToken } from "@/utils/auth-server"

// 预订请求数据类型
interface BookingRequestData {
  user_id: number
  hotel_id: number
  room_type: string
  check_in_date: string
  check_out_date: string
  guests_count: number
  companions?: string[]
  special_requests?: string
}

/**
 * 创建住宿预订
 * POST /api/accommodations/book
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const authResult = verifyAuthToken(request)
    if (!authResult.isValid) {
      return NextResponse.json(
        {
          code: 401,
          msg: authResult.error || "Authentication failed",
          message: "Please login to make a booking",
        },
        { status: 401 }
      )
    }

    // 解析请求数据
    let requestData: BookingRequestData
    try {
      requestData = (await request.json()) as BookingRequestData
    } catch (_error) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Invalid request data",
          message: "Failed to parse request data",
        },
        { status: 400 }
      )
    }

    // 验证必需字段
    const requiredFields: (keyof BookingRequestData)[] = [
      "user_id",
      "hotel_id",
      "room_type",
      "check_in_date",
      "check_out_date",
      "guests_count",
    ]
    const missingFields = requiredFields.filter((field) => !requestData[field])

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Missing required fields",
          message: `Missing required fields: ${missingFields.join(", ")}`,
        },
        { status: 400 }
      )
    }

    // 验证日期格式和逻辑
    const checkInDate = new Date(requestData.check_in_date)
    const checkOutDate = new Date(requestData.check_out_date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    if (checkInDate < today) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Invalid check-in date",
          message: "Check-in date cannot be in the past",
        },
        { status: 400 }
      )
    }

    if (checkOutDate <= checkInDate) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Invalid check-out date",
          message: "Check-out date must be after check-in date",
        },
        { status: 400 }
      )
    }

    // 验证客人数量
    if (requestData.guests_count < 1 || requestData.guests_count > 4) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Invalid guests count",
          message: "Guests count must be between 1 and 4",
        },
        { status: 400 }
      )
    }

    // 验证同行人用户名（如果提供）
    const companions = requestData.companions || []
    if (companions.length > 0) {
      // 在实际应用中，这里应该验证用户名是否存在
      // TODO: 实现用户名验证逻辑
      for (const companion of companions) {
        if (!companion || typeof companion !== "string" || companion.trim().length === 0) {
          return NextResponse.json(
            {
              code: 400,
              msg: "Invalid companion username",
              message: "All companion usernames must be valid",
            },
            { status: 400 }
          )
        }
      }
    }

    // 处理预订数据
    const processedData = {
      user_id: requestData.user_id,
      hotel_id: requestData.hotel_id,
      room_type: requestData.room_type.trim(),
      check_in_date: requestData.check_in_date,
      check_out_date: requestData.check_out_date,
      guests_count: requestData.guests_count,
      companions: companions.map((c: string) => c.trim()),
      special_requests: requestData.special_requests?.trim() || "",
      booking_time: new Date().toISOString(),
    }

    // 计算总价格（模拟）
    const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24))
    const roomPrices: { [key: string]: number } = {
      "Standard Room": 268,
      "King Room": 268,
    }
    const roomPrice = roomPrices[processedData.room_type] || 268
    const totalPrice = roomPrice * nights

    // 在实际应用中，这里应该：
    // 1. 检查房间可用性
    // 2. 将预订信息保存到数据库
    // 3. 发送确认邮件给用户
    // 4. 生成确认号码
    // TODO: 实现数据库保存和邮件发送逻辑

    // 模拟预订处理
    console.log("Accommodation booking data:", processedData)

    // 生成预订记录
    const bookingRecord = {
      booking_id: `booking_${Date.now()}`,
      user_id: processedData.user_id,
      hotel_id: processedData.hotel_id,
      hotel_name: "Sample Hotel Name", // 在实际应用中从数据库获取
      room_type: processedData.room_type,
      check_in_date: processedData.check_in_date,
      check_out_date: processedData.check_out_date,
      guests_count: processedData.guests_count,
      companions: processedData.companions,
      total_price: totalPrice,
      currency: "CNY",
      status: "pending",
      special_requests: processedData.special_requests,
      booking_time: processedData.booking_time,
      confirmation_number: `IFMB2025-${Date.now().toString().slice(-6)}`,
    }

    // 返回成功响应
    return NextResponse.json(
      {
        code: 200,
        msg: "Booking created successfully",
        message: "Your accommodation booking has been submitted and is being processed",
        data: bookingRecord,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Accommodation booking error:", error)

    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        message: "An error occurred while processing your booking. Please try again later.",
      },
      { status: 500 }
    )
  }
}

// 处理不支持的HTTP方法
export async function GET() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports POST requests",
    },
    { status: 405 }
  )
}
