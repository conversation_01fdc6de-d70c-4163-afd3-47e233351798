import { NextRequest, NextResponse } from "next/server"
import { verifyAuthToken } from "@/utils/auth-server"

/**
 * 获取酒店信息和房型数据
 * GET /api/accommodations/hotels
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const authResult = verifyAuthToken(request)
    if (!authResult.isValid) {
      return NextResponse.json(
        {
          code: 401,
          msg: authResult.error || "Authentication failed",
          message: "Please login to view hotel information",
        },
        { status: 401 }
      )
    }

    // 酒店数据（从 transportation 页面复用）
    const hotels = [
      {
        id: 1,
        name: "Huazhong Agricultural University International Academic Exchange Center（IAEC）",
        address:
          "Southeast corner of Xueyuan Road & Cuizhu Road Intersection, Huazhong Agricultural University, No.1 Shizishan Street, Hongshan District, Wuhan, Hubei Province",
        viewOnMapName: "华中农业大学国际学术交流中心",
        tel: "+86-27-87280141",
        distance: "On campus (5 min walk)",
        coordinates: { lat: 30.4745, lng: 114.368 },
        roomTypes: [
          {
            id: "iaec-standard",
            type: "Standard Room",
            price: 268,
            currency: "CNY",
            availableRooms: 60,
          },
          {
            id: "iaec-king",
            type: "King Room",
            price: 268,
            currency: "CNY",
            availableRooms: 40,
          },
        ],
        images: ["https://minioapi.bugmaker.me/ifmb-2025-public/hotels/hotel1.jpg"],
        description:
          "Located on campus, this hotel offers convenient access to the conference venue with modern amenities and comfortable accommodations.",
      },
      {
        id: 2,
        name: "Wuhan Nanhu Huanong Intercity Hotel",
        address:
          "1/F, Lanshenghui Research Building, No. 81 Nanhu Avenue, Hongshan Street Subdistrict, Hongshan District, Wuhan, Hubei Province",
        viewOnMapName: "武汉南湖华农城际酒店",
        tel: "+86-27-88000188",
        distance: "2.7 km (8 min shuttle)",
        coordinates: { lat: 30.452, lng: 114.359 },
        roomTypes: [
          {
            id: "nanhu-standard",
            type: "Standard Room",
            price: 380,
            currency: "CNY",
            availableRooms: 20,
          },
          {
            id: "nanhu-king",
            type: "King Room",
            price: 380,
            currency: "CNY",
            availableRooms: 40,
          },
        ],
        images: ["https://minioapi.bugmaker.me/ifmb-2025-public/hotels/hotel2.jpg"],
        description:
          "A modern intercity hotel with excellent facilities and convenient shuttle service to the conference venue.",
      },
      {
        id: 3,
        name: "Vienna Hotel (Wuhan Nanhu Huazhong Agricultural University Branch)",
        address: "No.99 Haogou, Nanhu Avenue, Shizishan Subdistrict, Hongshan District, Wuhan, Hubei Province",
        viewOnMapName: "维也纳酒店(武汉南湖华中农业大学店)",
        tel: "+86-27-87377666",
        distance: "3.0 km (9 min shuttle)",
        coordinates: { lat: 30.461, lng: 114.362 },
        roomTypes: [
          {
            id: "vienna-standard",
            type: "Standard Room",
            price: 280,
            currency: "CNY",
            availableRooms: 40,
          },
          {
            id: "vienna-king",
            type: "King Room",
            price: 280,
            currency: "CNY",
            availableRooms: 40,
          },
        ],
        images: ["https://minioapi.bugmaker.me/ifmb-2025-public/hotels/hotel3.jpg"],
        description:
          "A well-known hotel chain offering comfortable accommodations with reliable service and amenities.",
      },
      {
        id: 4,
        name: "City Comfort Inn (Wuhan Nanhu Huazhong Agricultural University Branch)",
        address: "No.312 Luoshi South Road, Shizishan Subdistrict, Hongshan District, Wuhan, Hubei Province",
        viewOnMapName: "城市便捷酒店(武汉南湖华中农业大学店)",
        tel: "+86-27-87773360",
        distance: "3.0 km (9 min shuttle)",
        coordinates: { lat: 30.458, lng: 114.345 },
        roomTypes: [
          {
            id: "comfort-standard",
            type: "Standard Room",
            price: 218,
            currency: "CNY",
            availableRooms: 14,
          },
          {
            id: "comfort-king",
            type: "King Room",
            price: 218,
            currency: "CNY",
            availableRooms: 35,
          },
        ],
        images: ["https://minioapi.bugmaker.me/ifmb-2025-public/hotels/hotel4.jpg"],
        description:
          "A budget-friendly option with essential amenities and convenient location near the conference venue.",
      },
    ]

    return NextResponse.json(
      {
        code: 200,
        msg: "Hotels retrieved successfully",
        message: "Hotel information loaded successfully",
        data: hotels,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Get hotels error:", error)

    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        message: "An error occurred while retrieving hotel information. Please try again later.",
      },
      { status: 500 }
    )
  }
}

// 处理不支持的HTTP方法
export async function POST() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}
