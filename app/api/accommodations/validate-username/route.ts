import { NextRequest, NextResponse } from "next/server"
import { verifyAuthToken } from "@/utils/auth-server"

/**
 * 验证用户名是否存在（用于同行人验证）
 * GET /api/accommodations/validate-username?username=xxx
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const authResult = verifyAuthToken(request)
    if (!authResult.isValid) {
      return NextResponse.json(
        {
          code: 401,
          msg: authResult.error || "Authentication failed",
          message: "Please login to validate usernames",
        },
        { status: 401 }
      )
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url)
    const username = searchParams.get("username")

    if (!username || username.trim().length === 0) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Missing username parameter",
          message: "Username parameter is required",
        },
        { status: 400 }
      )
    }

    const trimmedUsername = username.trim()

    // 验证用户名格式
    if (trimmedUsername.length < 3 || trimmedUsername.length > 20) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Invalid username format",
          message: "Username must be between 3 and 20 characters",
        },
        { status: 400 }
      )
    }

    // 在实际应用中，这里应该从数据库查询用户名是否存在
    // 目前返回模拟数据
    // TODO: 实现数据库查询逻辑

    // 模拟用户名验证
    const mockValidUsernames = [
      { username: "john_doe", name: "John Doe" },
      { username: "jane_smith", name: "Jane Smith" },
      { username: "test_user", name: "Test User" },
      { username: "demo_user", name: "Demo User" },
    ]

    const foundUser = mockValidUsernames.find((user) => user.username === trimmedUsername)

    if (foundUser) {
      return NextResponse.json(
        {
          code: 200,
          msg: "Username validation successful",
          message: "Username exists",
          data: {
            username: foundUser.username,
            exists: true,
            name: foundUser.name,
          },
        },
        { status: 200 }
      )
    } else {
      return NextResponse.json(
        {
          code: 200,
          msg: "Username validation successful",
          message: "Username does not exist",
          data: {
            username: trimmedUsername,
            exists: false,
          },
        },
        { status: 200 }
      )
    }
  } catch (error) {
    console.error("Username validation error:", error)

    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        message: "An error occurred while validating the username. Please try again later.",
      },
      { status: 500 }
    )
  }
}

// 处理不支持的HTTP方法
export async function POST() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}
