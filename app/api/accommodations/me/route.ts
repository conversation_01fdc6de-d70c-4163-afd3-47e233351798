import { NextRequest, NextResponse } from "next/server"
import { verifyAuthToken } from "@/utils/auth-server"

/**
 * 获取当前用户的住宿预订记录
 * GET /api/accommodations/me?user_id={user_id}
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const authResult = verifyAuthToken(request)
    if (!authResult.isValid) {
      return NextResponse.json(
        {
          code: 401,
          msg: authResult.error || "Authentication failed",
          message: "Please login to view your bookings",
        },
        { status: 401 }
      )
    }

    // 从查询参数获取用户ID
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    if (!userId) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Missing user_id parameter",
          message: "User ID is required",
        },
        { status: 400 }
      )
    }

    // 验证用户ID匹配 (确保用户只能查看自己的预订)
    // 在实际应用中，应该从JWT token中获取用户ID并与查询参数比较
    // TODO: 实现用户ID验证逻辑

    // 在实际应用中，这里应该从数据库查询用户的预订记录
    // 目前返回模拟数据
    // TODO: 实现数据库查询逻辑

    // 模拟用户预订记录 - 默认显示无预订状态
    const hasBooking = false // 设置为无预订，显示提醒信息

    if (!hasBooking) {
      return NextResponse.json(
        {
          code: 200,
          msg: "get booking successfully",
          message: "No booking found for this user",
          data: null,
        },
        { status: 200 }
      )
    }

    // 模拟返回预订数据（如果有预订的话）
    const mockBooking = {
      booking_id: `booking_${userId}_001`,
      user_id: parseInt(userId),
      hotel_id: 1,
      hotel_name: "Huazhong Agricultural University International Academic Exchange Center（IAEC）",
      chinese_name: "华中农业大学国际学术交流中心",
      room_type: "Standard Room",
      check_in_date: "2025-10-16",
      check_out_date: "2025-10-19",
      guests_count: 2,
      companions: ["john_doe"],
      total_price: 804, // 268 * 3 nights
      currency: "CNY",
      status: "confirmed" as const,
      special_requests: "Non-smoking room preferred",
      booking_time: "2025-01-15T10:30:00.000Z",
      confirmation_number: "IFMB2025-001",
      imageUrl: "/images/hotels/iaec-hotel.jpg",
      address: "Southeast corner of Xueyuan Road & Cuizhu Road Intersection, Huazhong Agricultural University",
      contact_phone: "+86-27-87280141",
    }

    return NextResponse.json(
      {
        code: 200,
        msg: "get booking successfully",
        message: "User booking retrieved successfully",
        data: mockBooking,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Get user booking error:", error)

    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        message: "An error occurred while retrieving your booking. Please try again later.",
      },
      { status: 500 }
    )
  }
}

// 处理不支持的HTTP方法
export async function POST() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}
