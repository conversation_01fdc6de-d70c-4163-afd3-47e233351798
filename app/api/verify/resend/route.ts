import { NextRequest, NextResponse } from "next/server"

/**
 * Resend Email Verification API
 * 重发邮箱验证邮件
 * 路由: POST /api/verify/resend
 */

// 验证JWT token的辅助函数
function verifyAuthToken(request: NextRequest): { isValid: boolean; userId?: string; error?: string } {
  try {
    const authHeader = request.headers.get("authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { isValid: false, error: "Missing or invalid authorization header" }
    }

    const token = authHeader.substring(7)
    if (!token) {
      return { isValid: false, error: "Missing JWT token" }
    }

    // 在实际应用中，这里应该验证JWT token的有效性
    // 目前返回一个模拟的用户ID
    // TODO: 实现真正的JWT验证逻辑
    return { isValid: true, userId: "mock_user_id" }
  } catch (_error) {
    return { isValid: false, error: "Token verification failed" }
  }
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const authResult = verifyAuthToken(request)
    if (!authResult.isValid) {
      return NextResponse.json(
        {
          code: 401,
          msg: authResult.error || "Authentication failed",
          message: "Please login to resend verification email",
        },
        { status: 401 }
      )
    }

    // 解析请求体
    let body: { email?: string }
    try {
      body = (await request.json()) as { email?: string }
    } catch (_error) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Invalid request body",
          message: "Failed to parse request data",
        },
        { status: 400 }
      )
    }

    const { email } = body

    // 验证邮箱地址
    if (!email) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Email is required",
          message: "Email address is required to resend verification",
        },
        { status: 400 }
      )
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Invalid email format",
          message: "Please provide a valid email address",
        },
        { status: 400 }
      )
    }

    // 在实际应用中，这里应该：
    // 1. 检查用户是否存在
    // 2. 检查邮箱是否已经验证
    // 3. 检查重发频率限制（防止滥用）
    // 4. 生成新的验证token
    // 5. 发送验证邮件
    // TODO: 实现真正的邮件发送逻辑

    // 模拟检查用户是否已经验证
    const isAlreadyVerified = Math.random() > 0.8 // 20%概率已验证

    if (isAlreadyVerified) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Email already verified",
          message: "Your email address is already verified",
        },
        { status: 400 }
      )
    }

    // 模拟频率限制检查
    const isRateLimited = Math.random() > 0.9 // 10%概率触发频率限制

    if (isRateLimited) {
      return NextResponse.json(
        {
          code: 429,
          msg: "Rate limit exceeded",
          message: "Please wait before requesting another verification email",
        },
        { status: 429 }
      )
    }

    // 模拟邮件发送
    console.log(`Sending verification email to: ${email}`)

    // 生成模拟的验证token
    const verificationToken = `verify_${Date.now()}_${Math.random().toString(36).substring(2)}`

    // 在实际应用中，这里会：
    // 1. 将token存储到数据库
    // 2. 发送包含验证链接的邮件
    console.log(`Generated verification token: ${verificationToken}`)

    return NextResponse.json(
      {
        code: 200,
        msg: "Verification email sent successfully",
        message:
          "A new verification email has been sent to your email address. Please check your inbox and spam folder.",
        data: {
          email: email,
          sentAt: new Date().toISOString(),
          // 在生产环境中不应该返回token
          debug: {
            token: verificationToken,
          },
        },
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Resend verification email error:", error)

    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        message: "An error occurred while sending verification email. Please try again later.",
      },
      { status: 500 }
    )
  }
}

// 处理不支持的HTTP方法
export async function GET() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports POST requests",
    },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports POST requests",
    },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports POST requests",
    },
    { status: 405 }
  )
}
