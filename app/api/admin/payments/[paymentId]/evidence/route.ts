import { NextRequest, NextResponse } from "next/server"
import { verifyAuthToken } from "@/utils/auth-server"

/**
 * 获取支付凭证
 * GET /api/admin/payments/[paymentId]/evidence
 */
export async function GET(request: NextRequest, context: { params: Promise<{ paymentId: string }> }) {
  try {
    const { paymentId } = await context.params

    // 验证用户认证
    const authResult = verifyAuthToken(request)
    if (!authResult.isValid) {
      return NextResponse.json(
        {
          code: 401,
          msg: authResult.error || "Authentication failed",
          message: "Please login to view payment evidence",
        },
        { status: 401 }
      )
    }

    // 验证管理员权限
    // TODO: 实现管理员权限验证逻辑
    // 在实际应用中，应该检查用户是否有管理员权限

    // 验证paymentId参数
    if (!paymentId || isNaN(Number(paymentId))) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Invalid payment ID",
          message: "Payment ID must be a valid number",
        },
        { status: 400 }
      )
    }

    // 在实际应用中，这里应该从数据库查询支付记录的凭证信息
    // 然后从MinIO或其他存储服务获取预签名URL
    // TODO: 实现数据库查询和MinIO预签名URL生成逻辑

    // 模拟从后端获取支付凭证URL
    // 在实际应用中，这应该是从数据库查询到的MinIO URL或预签名URL
    const mockEvidenceUrl = `https://minioapi.bugmaker.me/receipts/payment_${paymentId}_evidence.jpg`

    return NextResponse.json(
      {
        code: 200,
        msg: "Payment evidence retrieved successfully",
        message: "Payment evidence loaded successfully",
        data: {
          evidence: mockEvidenceUrl,
        },
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Get payment evidence error:", error)

    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        message: "An error occurred while retrieving payment evidence. Please try again later.",
      },
      { status: 500 }
    )
  }
}

// 处理不支持的HTTP方法
export async function POST() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}
