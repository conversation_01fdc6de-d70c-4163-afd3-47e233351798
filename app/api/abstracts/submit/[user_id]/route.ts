import { NextRequest, NextResponse } from "next/server"

/**
 * Abstract Submission API
 * 处理用户摘要提交请求
 * 路由: POST /api/abstracts/submit/:user_id
 */

// 支持的文件类型
const ALLOWED_FILE_TYPES = [
  "application/msword", // .doc
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
  "application/pdf", // .pdf
]

// 最大文件大小 (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024

// 验证JWT token的辅助函数
function verifyAuthToken(request: NextRequest): { isValid: boolean; userId?: string; error?: string } {
  try {
    const authHeader = request.headers.get("authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { isValid: false, error: "Missing or invalid authorization header" }
    }

    const token = authHeader.substring(7)
    if (!token) {
      return { isValid: false, error: "Missing JWT token" }
    }

    // 在实际应用中，这里应该验证JWT token的有效性
    // 目前返回一个模拟的用户ID
    // TODO: 实现真正的JWT验证逻辑
    return { isValid: true, userId: "mock_user_id" }
  } catch (_error) {
    return { isValid: false, error: "Token verification failed" }
  }
}

// 验证文件的辅助函数（暂时未使用，保留以备将来使用）
function _validateFile(file: File): { isValid: boolean; error?: string } {
  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return { isValid: false, error: "File size exceeds 10MB limit" }
  }

  // 检查文件类型
  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return { isValid: false, error: "Invalid file type. Only .doc, .docx, and .pdf files are allowed" }
  }

  return { isValid: true }
}

// 验证FormData数据的辅助函数
function validateFormData(formData: FormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // 验证必填字段
  const title = formData.get("title") as string
  if (!title || title.trim().length === 0) {
    errors.push("Abstract title is required")
  }

  const theme_id = formData.get("theme_id") as string
  if (!theme_id || isNaN(parseInt(theme_id))) {
    errors.push("Conference theme is required")
  }

  const publication_status = formData.get("publication_status") as string
  if (!publication_status) {
    errors.push("Publication status is required")
  } else if (!["unpublished", "partially", "published"].includes(publication_status)) {
    errors.push("Invalid publication status")
  }

  const oral_presentation = formData.get("oral_presentation")
  if (oral_presentation !== "true" && oral_presentation !== "false") {
    errors.push("Oral presentation field must be boolean")
  }

  const poster_presentation = formData.get("poster_presentation")
  if (poster_presentation !== "true" && poster_presentation !== "false") {
    errors.push("Poster presentation field must be boolean")
  }

  // 验证文件
  const abstractFile = formData.get("abstract") as File
  if (!abstractFile || abstractFile.size === 0) {
    errors.push("Abstract file is required")
  } else {
    const fileValidation = _validateFile(abstractFile)
    if (!fileValidation.isValid) {
      errors.push(fileValidation.error!)
    }
  }

  return { isValid: errors.length === 0, errors }
}

export async function POST(request: NextRequest, context: { params: Promise<{ user_id: string }> }) {
  try {
    const { user_id } = await context.params

    // 验证用户认证
    const authResult = verifyAuthToken(request)
    if (!authResult.isValid) {
      return NextResponse.json(
        {
          code: 401,
          msg: authResult.error || "Authentication failed",
          message: "Please login to submit abstracts",
        },
        { status: 401 }
      )
    }

    // 验证用户ID匹配 (确保用户只能为自己提交摘要)
    // 在实际应用中，应该从JWT token中获取用户ID并与路径参数比较
    // TODO: 实现用户ID验证逻辑

    // 解析FormData
    let formData: FormData
    try {
      formData = await request.formData()
    } catch (_error) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Invalid form data",
          message: "Failed to parse form data",
        },
        { status: 400 }
      )
    }

    // 验证表单数据
    const validation = validateFormData(formData)
    if (!validation.isValid) {
      return NextResponse.json(
        {
          code: 400,
          msg: "Validation failed",
          message: "Please check your input data",
          errors: validation.errors,
        },
        { status: 400 }
      )
    }

    // 检查用户邮箱验证状态
    // 在实际应用中，这里应该查询数据库检查用户的邮箱验证状态
    // TODO: 实现邮箱验证状态检查
    const isEmailVerified = true // 模拟邮箱已验证

    if (!isEmailVerified) {
      return NextResponse.json(
        {
          code: 403,
          msg: "Email verification required",
          message: "Please verify your email address before submitting abstracts",
        },
        { status: 403 }
      )
    }

    // 处理提交数据
    const processedData = {
      userId: user_id,
      title: (formData.get("title") as string).trim(),
      theme_id: parseInt(formData.get("theme_id") as string),
      publication_status: formData.get("publication_status") as string,
      oral_presentation: formData.get("oral_presentation") === "true",
      poster_presentation: formData.get("poster_presentation") === "true",
      abstractFile: formData.get("abstract") as File,
      submissionTime: new Date().toISOString(),
    }

    // 在实际应用中，这里应该：
    // 1. 将文件上传到文件存储服务 (如MinIO)
    // 2. 将摘要信息保存到数据库
    // 3. 发送确认邮件给用户
    // TODO: 实现文件存储和数据库保存逻辑

    // 模拟文件上传和数据保存
    console.log("Abstract submission data:", {
      ...processedData,
      abstractFile: {
        name: processedData.abstractFile.name,
        size: processedData.abstractFile.size,
        type: processedData.abstractFile.type,
      },
    })

    // 返回成功响应
    return NextResponse.json(
      {
        code: 200,
        msg: "Abstract submitted successfully",
        message: "Your abstract has been submitted and is under review",
        data: {
          submissionId: `abs_${Date.now()}`, // 模拟生成的提交ID
          title: processedData.title,
          theme_id: processedData.theme_id,
          publication_status: processedData.publication_status,
          oral_presentation: processedData.oral_presentation,
          poster_presentation: processedData.poster_presentation,
          fileName: processedData.abstractFile.name,
          submissionTime: processedData.submissionTime,
          status: "pending_review",
        },
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Abstract submission error:", error)

    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        message: "An error occurred while processing your submission. Please try again later.",
      },
      { status: 500 }
    )
  }
}

// 处理不支持的HTTP方法
export async function GET() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports POST requests",
    },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports POST requests",
    },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports POST requests",
    },
    { status: 405 }
  )
}
