import { NextRequest, NextResponse } from "next/server"

/**
 * Get User Abstract Submission API
 * 获取用户的摘要提交记录
 * 路由: GET /api/abstracts/get/:user_id
 */

// 验证JWT token的辅助函数
function verifyAuthToken(request: NextRequest): { isValid: boolean; userId?: string; error?: string } {
  try {
    const authHeader = request.headers.get("authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { isValid: false, error: "Missing or invalid authorization header" }
    }

    const token = authHeader.substring(7)
    if (!token) {
      return { isValid: false, error: "Missing JWT token" }
    }

    // 在实际应用中，这里应该验证JWT token的有效性
    // 目前返回一个模拟的用户ID
    // TODO: 实现真正的JWT验证逻辑
    return { isValid: true, userId: "mock_user_id" }
  } catch (_error) {
    return { isValid: false, error: "Token verification failed" }
  }
}

export async function GET(request: NextRequest, context: { params: Promise<{ user_id: string }> }) {
  try {
    const { user_id } = await context.params

    // 验证用户认证
    const authResult = verifyAuthToken(request)
    if (!authResult.isValid) {
      return NextResponse.json(
        {
          code: 401,
          msg: authResult.error || "Authentication failed",
          message: "Please login to view your submissions",
        },
        { status: 401 }
      )
    }

    // 验证用户ID匹配 (确保用户只能查看自己的提交)
    // 在实际应用中，应该从JWT token中获取用户ID并与路径参数比较
    // TODO: 实现用户ID验证逻辑

    // 在实际应用中，这里应该从数据库查询用户的摘要提交
    // 目前返回模拟数据
    // TODO: 实现数据库查询逻辑

    // 模拟用户没有提交的情况
    const hasSubmission = true // 暂时设置为总是有提交，用于测试

    if (!hasSubmission) {
      return NextResponse.json(
        {
          code: 200,
          msg: "get abstract successfully",
          message: "No submission found for this user",
          data: null,
        },
        { status: 200 }
      )
    }

    // 模拟返回现有提交数据
    const mockSubmission = {
      id: `abs_${user_id}_${Date.now()}`,
      title: "Sample Abstract Title",
      theme_id: 1,
      publication_status: "unpublished",
      oral_presentation: true,
      poster_presentation: false,
      fileName: "sample_abstract.pdf",
      submissionTime: new Date().toISOString(),
      status: "pending_review",
    }

    return NextResponse.json(
      {
        code: 200,
        msg: "get abstract successfully",
        message: "User submission retrieved successfully",
        data: mockSubmission,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Get user submission error:", error)

    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        message: "An error occurred while retrieving your submission. Please try again later.",
      },
      { status: 500 }
    )
  }
}

// 处理不支持的HTTP方法
export async function POST() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    {
      code: 405,
      msg: "Method not allowed",
      message: "This endpoint only supports GET requests",
    },
    { status: 405 }
  )
}
