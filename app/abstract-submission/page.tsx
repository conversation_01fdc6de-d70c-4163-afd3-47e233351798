import { Metadata } from "next"
import AbstractSubmissionClient from "@/components/abstract-submission/abstract-submission-client"

export const metadata: Metadata = {
  title: "Abstract Submission | IFMB 2025",
  description:
    "Submit your abstract for the International Forum on Maize Biology 2025. Learn about conference themes, submission guidelines, and poster presentation requirements.",
  keywords:
    "IFMB abstract submission, maize biology abstracts, corn research submission, IFMB 2025 posters, conference presentation",
}

/**
 * Abstract submission information page for IFMB 2025
 */
export default function AbstractSubmissionPage() {
  // Conference information
  const conferenceInfo = {
    title: "International Forum on Maize Biology",
    shortTitle: "IFMB 2025",
    dates: "October 16-20, 2025",
    location: "Wuhan, Hubei Province, China",
    venue: "Huazhong Agricultural University",
    websiteUrl: "https://ifmb.maizemeeting.com/",
    submissionDeadline: "September 15, 2025",
  }

  // Conference themes
  const conferenceThemes = [
    {
      id: 1,
      titleEn: "Multiple Omics and Genetic Control of Maize Complex Traits",
      icon: "dna",
      color: "blue",
    },
    {
      id: 2,
      titleEn: "Stress Biology",
      icon: "seedling",
      color: "green",
    },
    {
      id: 3,
      titleEn: "Development and Evolutionary Biology",
      icon: "shield-alt",
      color: "orange",
    },
    {
      id: 4,
      titleEn: "Big Data Mining and Intelligent Design Breeding",
      icon: "tree",
      color: "purple",
    },
    {
      id: 5,
      titleEn: "Maize Germplasm Innovation and Utilization",
      icon: "industry",
      color: "amber",
    },
  ]

  // 摘要征集要求
  const submissionRequirements = [
    {
      id: 1,
      title: "Registration and Submission",
      description:
        "Participants should register on the conference website (https://ifmb.maizemeeting.com/) and prepare English abstracts using the provided Word template. Submit abstracts online through the conference website. Each abstract should not exceed 700 words and should be contained within one page. Authors are responsible for the content.",
      icon: "user-edit",
      color: "blue",
    },
    {
      id: 2,
      title: "Submission Deadline",
      description: "The deadline for abstract submission is September 15, 2025.",
      icon: "calendar-alt",
      color: "red",
    },
    {
      id: 3,
      title: "Poster Presentation",
      description:
        "The conference will arrange unified poster presentation sessions. A poster display area will be set up with specifications of 90cm width × 120cm height. Participants should bring their posters to the venue, post them according to assigned numbers, and retrieve them after the conference.",
      icon: "presentation",
      color: "green",
    },
    {
      id: 4,
      title: "Student Participation",
      description:
        "The organizing committee encourages students and postdocs to actively submit abstracts. Young scientists and graduate students can choose to apply for oral presentations, short presentations, or poster displays on the conference website. The executive committee will select outstanding submissions for conference presentations and short talks, which will be presented in English.",
      icon: "graduation-cap",
      color: "purple",
    },
  ]

  return (
    <AbstractSubmissionClient
      conferenceInfo={conferenceInfo}
      conferenceThemes={conferenceThemes}
      submissionRequirements={submissionRequirements}
    />
  )
}
