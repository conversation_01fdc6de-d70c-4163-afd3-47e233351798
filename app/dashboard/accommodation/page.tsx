import { Metadata } from "next"
import RealtimeRouteGuard from "@/components/auth/realtime-route-guard"
import AccommodationClient from "@/components/dashboard/accommodation-client"

export const metadata: Metadata = {
  title: "Accommodation | Dashboard - IFMB 2025",
  description: "Book and manage your hotel accommodation for IFMB 2025",
}

export default function AccommodationPage() {
  return (
    <RealtimeRouteGuard forceRefresh={true} showAccessDenied={true}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Hotel Accommodation</h1>
          <p className="mt-1 text-gray-600">Book and manage your stay during the conference</p>
        </div>

        <AccommodationClient />
      </div>
    </RealtimeRouteGuard>
  )
}
