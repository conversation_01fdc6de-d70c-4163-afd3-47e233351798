import { Metadata } from "next"
import RealtimeRouteGuard from "@/components/auth/realtime-route-guard"
import AbstractSubmissionClient from "@/components/dashboard/abstract-submission-client"

export const metadata: Metadata = {
  title: "Abstract Submission | Dashboard - IFMB 2025",
  description: "Submit and manage your abstracts for IFMB 2025",
}

export default function SubmissionPage() {
  // 会议主题数据
  const conferenceThemes = [
    {
      id: 1,
      titleEn: "Multiple Omics and Genetic Control of Maize Complex Traits",
      icon: "dna",
      color: "blue",
    },
    {
      id: 2,
      titleEn: "Stress Biology",
      icon: "seedling",
      color: "green",
    },
    {
      id: 3,
      titleEn: "Development and Evolutionary Biology",
      icon: "shield-alt",
      color: "orange",
    },
    {
      id: 4,
      titleEn: "Big Data Mining and Intelligent Design Breeding",
      icon: "tree",
      color: "purple",
    },
    {
      id: 5,
      titleEn: "Maize Germplasm Innovation and Utilization",
      icon: "industry",
      color: "amber",
    },
  ]

  return (
    <RealtimeRouteGuard forceRefresh={true} showAccessDenied={true}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Abstract Submission</h1>
          <p className="mt-1 text-gray-600">Submit and manage your research abstracts for IFMB 2025</p>
        </div>

        <AbstractSubmissionClient conferenceThemes={conferenceThemes} />
      </div>
    </RealtimeRouteGuard>
  )
}
