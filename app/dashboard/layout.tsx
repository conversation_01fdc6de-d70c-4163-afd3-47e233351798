import { Metadata } from "next"
import DashboardSidebar from "@/components/dashboard/dashboard-sidebar"

export const metadata: Metadata = {
  title: "User Dashboard | IFMB 2025",
  description: "Manage your IFMB 2025 conference participation",
}

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col gap-8 md:flex-row">
          {/* Sidebar */}
          <div className="w-full shrink-0 md:w-64">
            <DashboardSidebar />
          </div>

          {/* Main Content */}
          <div className="flex-1">{children}</div>
        </div>
      </div>
    </div>
  )
}
