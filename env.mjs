import { createEnv } from "@t3-oss/env-nextjs"
import { z } from "zod"

export const env = createEnv({
  server: {
    ANALYZE: z
      .enum(["true", "false"])
      .optional()
      .transform((value) => value === "true"),
    AMAP_API_KEY: z.string().optional(),
    AMAP_SECRET: z.string().optional(),
    TURNSTILE_SECRET_KEY: z.string().optional(),
  },
  client: {
    NEXT_PUBLIC_API_BASE_URL: z.string().default("http://localhost:3001"),
    NEXT_PUBLIC_AMAP_API_KEY: z.string().optional(),
    NEXT_PUBLIC_TURNSTILE_SITE_KEY: z.string().optional(),
  },
  runtimeEnv: {
    // Server-side env vars
    ANALYZE: process.env.ANALYZE,
    AMAP_API_KEY: process.env.AMAP_API_KEY,
    AMAP_SECRET: process.env.AMAP_SECRET,
    TURNSTILE_SECRET_KEY: process.env.TURNSTILE_SECRET_KEY,

    // Client-side env vars
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    NEXT_PUBLIC_AMAP_API_KEY: process.env.NEXT_PUBLIC_AMAP_API_KEY,
    NEXT_PUBLIC_TURNSTILE_SITE_KEY: process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY,
  },
})
