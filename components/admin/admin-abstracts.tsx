"use client"

import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { api } from "@/utils/api"

// Abstract submission data type from API
type AbstractSubmission = {
  abstract_id: number
  user_id: number
  title: string
  publication_status: string
  oral_presentation: boolean
  poster_presentation: boolean
  abstract_path: string
  submit_time: string
  update_time: string
  theme: string
  id: number
  username: string
  name: string
  gender: string
  country: string
  organization: string
}

// Statistics data type
type AbstractStatistics = {
  total: number
  oral: number
  poster: number
}

// API response types
type ApiResponse<T> = {
  code: number
  msg: string
  data: T
}

export default function AdminAbstractsPage() {
  const [abstracts, setAbstracts] = useState<AbstractSubmission[]>([])
  const [statistics, setStatistics] = useState<AbstractStatistics>({ total: 0, oral: 0, poster: 0 })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [themeFilter, setThemeFilter] = useState<string>("all")

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const result = await api.get<AbstractStatistics>("/api/admin/abstract/statistics")
      setStatistics(result)
    } catch (error) {
      console.error("Failed to fetch statistics:", error)
    }
  }

  // Fetch abstracts
  const fetchAbstracts = async () => {
    try {
      const result = await api.get<{ abstracts: AbstractSubmission[]; total: number }>("/api/admin/abstracts")
      setAbstracts(result.abstracts || [])
    } catch (error) {
      console.error("Failed to fetch abstracts:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStatistics()
    fetchAbstracts()
  }, [])

  // Filter abstracts
  const filteredAbstracts = abstracts.filter((abstract) => {
    const matchesSearch =
      abstract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      abstract.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      abstract.organization.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesTheme = themeFilter === "all" || abstract.theme === themeFilter

    return matchesSearch && matchesTheme
  })

  // Get presentation type display
  const getPresentationType = (oral: boolean, poster: boolean) => {
    if (oral && poster) return "Oral + Poster"
    if (oral) return "Oral Presentation"
    if (poster) return "Poster Presentation"
    return "Not Specified"
  }

  // Get publication status display
  const getPublicationStatus = (status: string) => {
    switch (status) {
      case "published":
        return "Published"
      case "unpublished":
        return "Unpublished"
      case "partially":
        return "Partially Published"
      default:
        return status
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Title */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Abstract Submissions</h1>
          <p className="text-gray-600">View and download abstract submissions for IFMB 2025</p>
        </div>
        <Button>
          <i className="fas fa-download mr-2"></i>
          Export All
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Submissions</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{statistics.total}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <i className="fas fa-file-alt text-xl text-blue-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Poster Presentation</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{statistics.poster}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <i className="fas fa-image text-xl text-green-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Oral Presentation</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{statistics.oral}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                <i className="fas fa-microphone text-xl text-purple-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="flex-1">
              <Input
                placeholder="Search by title, author, or organization..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <Select value={themeFilter} onValueChange={setThemeFilter}>
                <SelectTrigger className="w-64">
                  <SelectValue placeholder="Filter by Theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Themes</SelectItem>
                  <SelectItem value="Genetics and Genomics">Genetics and Genomics</SelectItem>
                  <SelectItem value="Development and Evolutionary Biology">
                    Development and Evolutionary Biology
                  </SelectItem>
                  <SelectItem value="Plant Physiology and Biochemistry">Plant Physiology and Biochemistry</SelectItem>
                  <SelectItem value="Breeding and Biotechnology">Breeding and Biotechnology</SelectItem>
                  <SelectItem value="Sustainable Agriculture">Sustainable Agriculture</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Abstract List */}
      <Card>
        <CardHeader>
          <CardTitle>Abstract Submissions ({filteredAbstracts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-500">Loading abstracts...</div>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAbstracts.map((abstract) => (
                <div key={abstract.abstract_id} className="rounded-lg border border-gray-200 p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="mb-2 flex items-center gap-2">
                        <h3 className="font-semibold text-gray-900">{abstract.title}</h3>
                        <Badge className="border-blue-200 bg-blue-100 text-blue-800">
                          {getPresentationType(abstract.oral_presentation, abstract.poster_presentation)}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 gap-2 text-sm text-gray-600 md:grid-cols-2">
                        <div>
                          <span className="font-medium">Author:</span> {abstract.name} ({abstract.username})
                        </div>
                        <div>
                          <span className="font-medium">Organization:</span> {abstract.organization}
                        </div>
                        <div>
                          <span className="font-medium">Theme:</span> {abstract.theme}
                        </div>
                        <div>
                          <span className="font-medium">Submission Date:</span>{" "}
                          {new Date(abstract.submit_time).toLocaleDateString()}
                        </div>
                        <div>
                          <span className="font-medium">Country:</span> {abstract.country}
                        </div>
                        <div>
                          <span className="font-medium">Publication Status:</span>{" "}
                          {getPublicationStatus(abstract.publication_status)}
                        </div>
                      </div>
                    </div>
                    <div className="ml-4 flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          // abstract_path should already be a full URL from the backend
                          window.open(abstract.abstract_path, "_blank")
                        }}
                      >
                        <i className="fas fa-download mr-1"></i>
                        Download
                      </Button>
                      <Button size="sm" variant="outline">
                        <i className="fas fa-eye mr-1"></i>
                        View Details
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              {filteredAbstracts.length === 0 && !loading && (
                <div className="py-8 text-center text-gray-500">No abstracts found matching your criteria.</div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
