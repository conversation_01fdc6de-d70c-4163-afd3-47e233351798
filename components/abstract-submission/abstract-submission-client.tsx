"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { fadeIn, staggerContainer } from "@/lib/animations"

// Types
type ConferenceInfo = {
  title: string
  shortTitle: string
  dates: string
  location: string
  venue: string
  websiteUrl: string
  submissionDeadline: string
}

type ConferenceTheme = {
  id: number
  titleEn: string
  icon: string
  color: string
}

type SubmissionRequirement = {
  id: number
  title: string
  description: string
  icon: string
  color: string
}

type AbstractSubmissionClientProps = {
  conferenceInfo: ConferenceInfo
  conferenceThemes: ConferenceTheme[]
  submissionRequirements: SubmissionRequirement[]
}

// Color mapping for themes and requirements
const colorClasses = {
  blue: "bg-blue-100 text-blue-800 border-blue-200",
  green: "bg-green-100 text-green-800 border-green-200",
  orange: "bg-orange-100 text-orange-800 border-orange-200",
  purple: "bg-purple-100 text-purple-800 border-purple-200",
  amber: "bg-amber-100 text-amber-800 border-amber-200",
  red: "bg-red-100 text-red-800 border-red-200",
}

const iconColorClasses = {
  blue: "text-blue-600 bg-blue-100",
  green: "text-green-600 bg-green-100",
  orange: "text-orange-600 bg-orange-100",
  purple: "text-purple-600 bg-purple-100",
  amber: "text-amber-600 bg-amber-100",
  red: "text-red-600 bg-red-100",
}

export default function AbstractSubmissionClient({
  conferenceInfo,
  conferenceThemes,
  submissionRequirements,
}: AbstractSubmissionClientProps) {
  return (
    <main className="pb-20">
      {/* Hero Section */}
      <section className="relative overflow-hidden pt-32 pb-16 text-white">
        <div className="absolute inset-0">
          <Image
            src="https://minioapi.bugmaker.me/ifmb-2025-public/home-page.jpg"
            alt="Conference Background"
            fill
            priority
            className="object-cover object-top"
          />
          <div className="absolute inset-0 bg-black/30 backdrop-blur-sm"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-green-900/30"></div>
          <div
            className="absolute inset-0 opacity-10"
            style={{ backgroundImage: `url('https://minioapi.bugmaker.me/ifmb-2025-public/pattern.svg')` }}
          ></div>
        </div>
        <div className="relative z-10 container mx-auto px-6">
          <div className="mx-auto max-w-4xl p-8 text-center">
            <Badge className="mb-4 border-green-400/50 bg-green-600/40 px-3 py-1 text-white">
              {conferenceInfo.shortTitle}
            </Badge>
            <h1 className="mb-6 text-4xl font-bold md:text-5xl">Abstract Submission</h1>
            <p className="mb-8 text-lg text-green-100">
              Submit your research abstract for IFMB 2025 and share your findings with the global maize biology
              community
            </p>
          </div>
        </div>
      </section>

      {/* Conference Themes Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-6xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Conference Themes</h2>
              <p className="text-lg text-gray-600">Choose your research area for abstract submission</p>
            </motion.div>

            <div className="flex flex-wrap justify-center gap-6">
              {conferenceThemes.map((theme, index) => (
                <motion.div
                  key={theme.id}
                  variants={fadeIn("up", 0.1 * index)}
                  className="w-full md:w-[calc(50%-12px)] lg:w-[calc(33.333%-16px)]"
                >
                  <Card className="h-full transition-all duration-300 hover:shadow-lg">
                    <CardHeader className="pb-4">
                      <div className="flex items-start gap-4">
                        <div
                          className={`flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg ${
                            iconColorClasses[theme.color as keyof typeof iconColorClasses]
                          }`}
                        >
                          <i className={`fas fa-${theme.icon} text-lg`}></i>
                        </div>
                        <div className="flex-1">
                          <Badge className={`mb-2 ${colorClasses[theme.color as keyof typeof colorClasses]}`}>
                            Theme {theme.id}
                          </Badge>
                          <CardTitle className="text-lg leading-tight">{theme.titleEn}</CardTitle>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600">Research focus area for abstract submission</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Submission Requirements Section */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-6xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Submission Guidelines</h2>
              <p className="text-lg text-gray-600">Important requirements for abstract submission</p>
            </motion.div>

            <div className="grid gap-8 md:grid-cols-2">
              {submissionRequirements.map((requirement, index) => (
                <motion.div key={requirement.id} variants={fadeIn("up", 0.1 * index)}>
                  <Card className="h-full">
                    <CardHeader>
                      <div className="flex items-start gap-4">
                        <div
                          className={`flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg ${
                            iconColorClasses[requirement.color as keyof typeof iconColorClasses]
                          }`}
                        >
                          <i className={`fas fa-${requirement.icon} text-lg`}></i>
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-xl">{requirement.title}</CardTitle>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="leading-relaxed text-gray-700">{requirement.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Important Information */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Important Information</h2>
              <p className="text-lg text-gray-600">Key details about abstract submission and presentation</p>
            </motion.div>

            <div className="grid gap-8 md:grid-cols-2">
              <motion.div variants={fadeIn("up", 0.2)}>
                <Card className="h-full border-red-200 bg-red-50 p-6">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center text-red-700">
                      <i className="fas fa-calendar-alt mr-3"></i>
                      Submission Deadline
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-2 text-2xl font-bold text-red-600">{conferenceInfo.submissionDeadline}</div>
                    <p className="text-sm text-red-700">
                      All abstracts must be submitted before this date. Late submissions will not be accepted.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={fadeIn("up", 0.3)}>
                <Card className="h-full border-blue-200 bg-blue-50 p-6">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center text-blue-700">
                      <i className="fas fa-presentation mr-3"></i>
                      Poster Specifications
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <i className="fas fa-ruler mr-2 text-blue-600"></i>
                        <span className="text-gray-700">Size: 90cm (W) × 120cm (H)</span>
                      </div>
                      <div className="flex items-center">
                        <i className="fas fa-language mr-2 text-blue-600"></i>
                        <span className="text-gray-700">Language: English</span>
                      </div>
                      <div className="flex items-center">
                        <i className="fas fa-file-alt mr-2 text-blue-600"></i>
                        <span className="text-gray-700">Max: 1 page</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-green-50 py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl text-center"
          >
            <motion.div variants={fadeIn("up", 0.1)}>
              <h2 className="mb-6 text-3xl font-bold text-gray-900">Ready to Submit Your Abstract?</h2>
              <p className="mb-8 text-lg text-gray-600">
                Join the premier international conference on maize biology research and share your findings with leading
                scientists worldwide.
              </p>
              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <Link href="/dashboard/submission">
                  <Button className="bg-green-600 px-8 py-3 text-lg text-white hover:bg-green-700">
                    <i className="fas fa-paper-plane mr-2"></i>
                    Submit Now
                  </Button>
                </Link>
                <Link href="/registration">
                  <Button
                    variant="outline"
                    className="border-green-600 px-8 py-3 text-lg text-green-600 hover:bg-green-50"
                  >
                    <i className="fas fa-user-plus mr-2"></i>
                    Register for Conference
                  </Button>
                </Link>
              </div>
              <div className="mt-6 flex items-center justify-center gap-2 text-yellow-600">
                <i className="fas fa-clock"></i>
                <span className="font-medium">Deadline: {conferenceInfo.submissionDeadline}</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </main>
  )
}
