"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { useEffect, useState } from "react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { useToast } from "@/components/ui/toast"
import { positions } from "@/data/positions"
import { getUserProfile, updateUserEmail, updateUserProfile, UpdateUserProfileRequest } from "@/utils/users-api"

// 模拟用户类型
type User = {
  id: string
  name: string
  email: string
  role: "user" | "admin"
  email_verified?: boolean
}

// 扩展的用户资料类型
type UserProfile = {
  id: string
  name: string
  email: string
  role: "user" | "admin"
  email_verified?: boolean
  organization?: string
  position?: string
  phone?: string
  bio?: string
  expertise?: string[]
  country?: string
  membership?: string
  interest?: string
}

type ProfileStats = {
  lastLogin: string
  memberSince: string
  documentsDownloaded: number
}

export default function UserProfile() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  // 添加原始资料备份，用于取消编辑时恢复
  const [originalProfile, setOriginalProfile] = useState<UserProfile | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState<ProfileStats | null>(null)
  const [expertiseInput, setExpertiseInput] = useState<string>("")
  const [_avatarFile, _setAvatarFile] = useState<File | null>(null)
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null)
  // 在React 19中，ref是一个常规prop，不再通过element.ref访问
  // 使用useState和回调函数来存储DOM元素引用
  const [fileInput, setFileInput] = useState<HTMLInputElement | null>(null)
  const [isResending, setIsResending] = useState(false)
  const [lastSentTime, setLastSentTime] = useState<number | null>(null)
  const [remainingTime, setRemainingTime] = useState(0)
  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const [isEditingEmail, setIsEditingEmail] = useState(false)
  const [newEmail, setNewEmail] = useState("")
  const { addToast } = useToast()

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        // 检查用户登录状态
        const userData = localStorage.getItem("user")
        if (!userData) {
          setIsInitialLoading(false)
          window.location.href = "/login"
          return
        }

        let parsedData
        try {
          parsedData = JSON.parse(userData) as unknown
        } catch (_jsonError) {
          console.error("Invalid JSON in localStorage:", _jsonError)
          setIsInitialLoading(false)
          window.location.href = "/login"
          return
        }

        const parsedDataTyped = parsedData as Record<string, unknown>
        const basicUserInfo = parsedDataTyped.user_info || (parsedDataTyped.data as Record<string, unknown>)?.user_info
        const authToken =
          (parsedDataTyped.token as string) || ((parsedDataTyped.data as Record<string, unknown>)?.token as string)

        if (!basicUserInfo || !authToken) {
          console.error("User info or token not found in localStorage")
          setIsInitialLoading(false)
          window.location.href = "/login"
          return
        }

        // 设置基本用户信息
        const basicUserInfoTyped = basicUserInfo as Record<string, unknown>
        const roleValue = (basicUserInfoTyped.role as string) || "user"
        setUser({
          id: (basicUserInfoTyped.id as string).toString(),
          name: basicUserInfoTyped.name as string,
          email: basicUserInfoTyped.email as string,
          role: roleValue === "admin" || roleValue === "user" ? roleValue : "user",
          email_verified: (basicUserInfoTyped.email_verified as boolean) === true,
        })

        // 从 API 获取完整的用户资料
        const userProfileData = await getUserProfile((basicUserInfoTyped.id as string).toString(), authToken)

        // 构建个人资料数据
        const profileRoleValue = userProfileData.role || "user"
        const profileData: UserProfile = {
          id: userProfileData.id.toString(),
          name: userProfileData.name,
          email: userProfileData.email,
          role: profileRoleValue === "admin" || profileRoleValue === "user" ? profileRoleValue : "user",
          email_verified: userProfileData.email_verified === true,
          organization: userProfileData.organization,
          position: userProfileData.position,
          phone: userProfileData.phone,
          bio: userProfileData.bio,
          expertise: userProfileData.expertise || [],

          interest: userProfileData.interest,
          country: userProfileData.country,
          membership: userProfileData.membership,
        }

        setProfile(profileData)

        // 初始化expertise输入框
        if (profileData.expertise && profileData.expertise.length > 0) {
          setExpertiseInput(profileData.expertise.join(", "))
        }

        // 设置统计数据
        const userCreateTime = new Date(userProfileData.create_time || new Date())
        const formattedDate = userCreateTime.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        })

        setStats({
          lastLogin: new Date().toISOString().split("T")[0] || "", // 今天的日期作为上次登录
          memberSince: formattedDate,
          documentsDownloaded: 0,
        })

        // 数据加载完成
        setIsInitialLoading(false)
      } catch (_error) {
        console.error("Error fetching user profile:", _error)
        // 如果 API 调用失败，可以考虑回退到 localStorage 数据或重定向到登录页
        addToast({
          type: "error",
          title: "Loading Failed",
          message: "Unable to load user profile. Please refresh the page and try again.",
        })
        setIsInitialLoading(false)
      }
    }

    fetchUserProfile()
  }, [])

  // 初始化重发验证邮件的状态
  useEffect(() => {
    const savedTime = localStorage.getItem("profile-verification-sent-time")
    if (savedTime) {
      const sentTime = parseInt(savedTime, 10)
      const elapsed = Date.now() - sentTime
      const cooldownPeriod = 120000 // 120秒

      if (elapsed < cooldownPeriod) {
        setLastSentTime(sentTime)
        setRemainingTime(Math.ceil((cooldownPeriod - elapsed) / 1000))
      } else {
        // 如果超过120秒，清除存储的时间
        localStorage.removeItem("profile-verification-sent-time")
      }
    }
  }, [])

  // 倒计时逻辑
  useEffect(() => {
    if (remainingTime > 0) {
      const timer = setTimeout(() => {
        setRemainingTime((prev) => {
          if (prev <= 1) {
            setLastSentTime(null)
            localStorage.removeItem("profile-verification-sent-time")
            return 0
          }
          return prev - 1
        })
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [remainingTime])

  // 重发验证邮件功能
  const handleResendVerification = async () => {
    if (isResending || remainingTime > 0) return

    setIsResending(true)
    try {
      // 从 localStorage 获取用户信息和 token
      const userData = localStorage.getItem("user")
      if (!userData) {
        addToast({
          type: "error",
          title: "Authentication Failed",
          message: "Please log in again before sending verification email.",
        })
        return
      }

      // 添加更健壮的JSON解析错误处理
      let parsedData
      try {
        parsedData = JSON.parse(userData) as unknown
      } catch (_jsonError) {
        addToast({
          type: "error",
          title: "Data Format Error",
          message: "Invalid user data format. Please log in again.",
        })
        return
      }

      const parsedDataTyped = parsedData as Record<string, unknown>
      const token =
        (parsedDataTyped.token as string) || ((parsedDataTyped.data as Record<string, unknown>)?.token as string)

      if (!token) {
        addToast({
          type: "error",
          title: "Authentication Failed",
          message: "Authentication token not found. Please log in again.",
        })
        return
      }

      // 调用后端 API 重发验证邮件
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/verify/resend`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          email: (parsedDataTyped.user_info as Record<string, unknown>)?.email as string,
        }),
      })

      // 添加更健壮的API响应JSON解析错误处理
      let data
      try {
        // 首先检查响应文本是否为空
        const responseText = await response.text()
        if (!responseText || responseText.trim() === "") {
          // 如果响应为空，直接处理
          if (response.ok) {
            const currentTime = Date.now()
            setLastSentTime(currentTime)
            setRemainingTime(120) // 120秒倒计时
            localStorage.setItem("profile-verification-sent-time", currentTime.toString())

            addToast({
              type: "success",
              title: "Email Sent",
              message: "Verification email has been sent. Please check your inbox.",
            })
          } else {
            addToast({
              type: "error",
              title: "Send Failed",
              message: "Failed to send verification email. Please try again later.",
            })
          }
          return
        }

        // 尝试解析JSON
        data = JSON.parse(responseText)

        if (response.ok) {
          const currentTime = Date.now()
          setLastSentTime(currentTime)
          setRemainingTime(120) // 120秒倒计时
          localStorage.setItem("profile-verification-sent-time", currentTime.toString())

          addToast({
            type: "success",
            title: "Email Sent",
            message: "Verification email has been sent. Please check your inbox.",
          })
        } else {
          addToast({
            type: "error",
            title: "Send Failed",
            message:
              ((data as Record<string, unknown>)?.msg as string) ||
              ((data as Record<string, unknown>)?.message as string) ||
              "Failed to send verification email. Please try again later.",
          })
        }
      } catch (_jsonError) {
        // 即使解析失败，也根据响应状态给出反馈
        if (response.ok) {
          const currentTime = Date.now()
          setLastSentTime(currentTime)
          setRemainingTime(120) // 120秒倒计时
          localStorage.setItem("profile-verification-sent-time", currentTime.toString())

          addToast({
            type: "success",
            title: "Email Sent",
            message: "Verification email has been sent. Please check your inbox.",
          })
        } else {
          addToast({
            type: "error",
            title: "Send Failed",
            message: "Failed to send verification email. Please try again later.",
          })
        }
      }
    } catch (_error) {
      addToast({
        type: "error",
        title: "Operation Failed",
        message: "An error occurred while resending verification email. Please try again later.",
      })
    } finally {
      setIsResending(false)
    }
  }

  // 修改邮箱功能
  const handleEmailChange = async () => {
    if (!newEmail.trim()) {
      addToast({
        type: "error",
        title: "Invalid Email",
        message: "Please enter a valid email address.",
      })
      return
    }

    // 简单的邮箱格式验证
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
    if (!emailRegex.test(newEmail)) {
      addToast({
        type: "error",
        title: "Invalid Email",
        message: "Please enter a valid email address.",
      })
      return
    }

    try {
      // 获取认证令牌
      const userData = localStorage.getItem("user")
      if (!userData) {
        addToast({
          type: "error",
          title: "Authentication Failed",
          message: "Please log in again.",
        })
        return
      }

      const parsedData = JSON.parse(userData) as Record<string, unknown>
      const authToken = (parsedData.token as string) || ((parsedData.data as Record<string, unknown>)?.token as string)

      if (!authToken) {
        addToast({
          type: "error",
          title: "Authentication Failed",
          message: "Authentication token not found. Please log in again.",
        })
        return
      }

      // 调用专门的邮箱更新 API
      await updateUserEmail(profile!.id, newEmail, authToken)

      // 更新本地状态
      const updatedProfile = { ...profile!, email: newEmail, email_verified: false }
      setProfile(updatedProfile)
      setOriginalProfile(updatedProfile)
      setIsEditingEmail(false)
      setNewEmail("")

      addToast({
        type: "success",
        title: "Email Updated",
        message: "Your email has been updated successfully. Please verify your new email address.",
      })
    } catch (error) {
      console.error("Error updating email:", error)
      addToast({
        type: "error",
        title: "Update Failed",
        message: "Failed to update email. Please try again.",
      })
    }
  }

  const handleSave = async () => {
    if (!profile) return

    setLoading(true)

    // 确保expertise字段已经正确处理
    let updatedProfile: UserProfile = { ...profile }
    if (isEditing && expertiseInput.trim() !== "") {
      const expertiseArray = expertiseInput
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item !== "")
      updatedProfile = { ...updatedProfile, expertise: expertiseArray }
    }

    try {
      // 获取认证令牌
      const userData = localStorage.getItem("user")
      if (!userData) {
        throw new Error("Authentication token not found. Please log in again.")
      }

      let parsedData
      try {
        parsedData = JSON.parse(userData) as unknown
      } catch (_jsonError) {
        throw new Error("Invalid user data format. Please log in again.")
      }

      const parsedDataTyped = parsedData as Record<string, unknown>
      const authToken =
        (parsedDataTyped.token as string) || ((parsedDataTyped.data as Record<string, unknown>)?.token as string)
      if (!authToken) {
        throw new Error("Authentication token not found. Please log in again.")
      }

      // 准备更新数据，只包含可编辑的字段，使用后端期望的字段名
      const updateData: UpdateUserProfileRequest = {
        name: updatedProfile.name,
        organization: updatedProfile.organization,
        position: updatedProfile.position,
        bio: updatedProfile.bio,
        expertise: updatedProfile.expertise, // 前后端统一使用 expertise
        interest: updatedProfile.interest,
      }

      // 调用新的 API 更新用户资料
      const result = await updateUserProfile(profile.id, updateData, authToken)

      // 如果后端返回了完整的用户数据，使用它；否则使用当前更新的数据
      let finalProfile: UserProfile

      if (result && typeof result === "object" && result.id) {
        // 后端返回了完整数据，使用 API 返回的数据
        const resultRoleValue = result.role || "user"
        finalProfile = {
          id: result.id.toString(),
          name: result.name,
          email: result.email,
          role: resultRoleValue === "admin" || resultRoleValue === "user" ? resultRoleValue : "user",
          email_verified: result.email_verified === true,
          organization: result.organization,
          position: result.position,
          phone: result.phone,
          bio: result.bio,
          expertise: result.expertise || [],

          interest: result.interest,
          country: result.country,
          membership: result.membership,
        }
      } else {
        // 后端只返回成功消息，使用当前更新的数据
        finalProfile = updatedProfile
      }

      // 更新成功后，更新原始资料备份
      setOriginalProfile(finalProfile)
      setProfile(finalProfile)
      setIsEditing(false)
      addToast({
        type: "success",
        title: "Update Successful",
        message: "Profile has been successfully updated!",
      })
    } catch (_error) {
      console.error("Error updating profile:", _error)
      addToast({
        type: "error",
        title: "Update Failed",
        message: "Update failed. Please try again.",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof UserProfile, value: string | string[]) => {
    if (profile) {
      setProfile({ ...profile, [field]: value })
    }
  }

  // 不再需要计算资料完成度

  // 显示初始加载状态
  if (isInitialLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mb-4 inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-green-600 border-r-transparent"></div>
          <h1 className="mb-2 text-xl font-medium text-gray-900">Loading Profile...</h1>
          <p className="text-gray-600">Please wait while we load your profile information.</p>
        </div>
      </div>
    )
  }

  // 如果加载完成但没有用户数据，显示登录提示
  if (!user || !profile || !stats) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-gray-900">Please Login</h1>
          <p className="mb-4 text-gray-600">You need to login to access your profile.</p>
          <Link href="/login">
            <Button>Login</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="container mx-auto max-w-6xl px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="mb-2 text-3xl font-bold text-gray-900">My Profile</h1>
              <p className="text-gray-600">Manage your personal information and conference details</p>
            </div>
            <div className="flex space-x-3">
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    onClick={() => {
                      // 取消编辑，恢复原始资料
                      if (originalProfile) {
                        setProfile(originalProfile)
                      }
                      setIsEditing(false)
                    }}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleSave} disabled={loading}>
                    {loading ? "Saving..." : "Save Changes"}
                  </Button>
                </>
              ) : (
                <Button
                  onClick={() => {
                    // 当进入编辑模式时，保存原始资料副本
                    if (profile) {
                      setOriginalProfile({ ...profile })
                    }

                    setIsEditing(true)

                    // 初始化expertiseInput
                    if (profile?.expertise && profile.expertise.length > 0) {
                      setExpertiseInput(profile.expertise.join(", "))
                    } else {
                      setExpertiseInput("")
                    }
                  }}
                >
                  <i className="fas fa-edit mr-2"></i>
                  Edit Profile
                </Button>
              )}
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
          {/* Left Sidebar - Profile Summary */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="space-y-6 lg:col-span-1"
          >
            {/* Profile Card */}
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  {/* 头像部分 - 可点击更换 */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <div className="group relative mx-auto mb-4 h-24 w-24 cursor-pointer">
                        {avatarPreview ? (
                          <Image
                            src={avatarPreview}
                            alt="Profile"
                            width={96}
                            height={96}
                            className="h-24 w-24 rounded-full border-2 border-green-500 object-cover"
                          />
                        ) : (
                          <div className="flex h-24 w-24 items-center justify-center rounded-full bg-green-600 text-2xl font-bold text-white">
                            {profile.name.charAt(0).toUpperCase()}
                          </div>
                        )}
                        <div className="bg-opacity-50 absolute inset-0 flex items-center justify-center rounded-full bg-black opacity-0 transition-opacity group-hover:opacity-100">
                          <span className="text-xs text-white">Change Photo</span>
                        </div>
                      </div>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Change Profile Photo</DialogTitle>
                      </DialogHeader>
                      <div className="mt-4 space-y-4">
                        <input
                          type="file"
                          ref={(el) => setFileInput(el)}
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0]
                            if (file) {
                              _setAvatarFile(file)
                              const reader = new FileReader()
                              reader.onloadend = () => {
                                setAvatarPreview(reader.result as string)
                              }
                              reader.readAsDataURL(file)
                            }
                          }}
                        />
                        <Button onClick={() => fileInput?.click()} className="w-full">
                          Select Image
                        </Button>
                        {avatarPreview && (
                          <div className="text-center">
                            <Image
                              src={avatarPreview}
                              alt="Preview"
                              width={128}
                              height={128}
                              className="mx-auto h-32 w-32 rounded-full border-2 border-green-500 object-cover"
                            />
                          </div>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                  <h2 className="text-xl font-bold text-gray-900">{profile.name}</h2>
                  {/*<p className="text-gray-600">{profile.position}</p>*/}
                  <p className="mt-1 text-sm text-gray-500">{profile.organization}</p>

                  <div className="mt-4 space-y-2">
                    <Badge variant="secondary" className="w-full justify-center">
                      {profile.membership}
                    </Badge>
                    <Badge variant={profile.role === "admin" ? "default" : "outline"} className="w-full justify-center">
                      {profile.role === "admin" ? "Administrator" : "Member"}
                    </Badge>
                  </div>

                  <div className="mt-6 space-y-2">
                    <Link href="/dashboard">
                      <Button variant="outline" className="w-full">
                        <i className="fas fa-tachometer-alt mr-2"></i>
                        Dashboard
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="lg:col-span-3"
          >
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <Label htmlFor="name">Full Name</Label>
                      <p className="mt-1 text-sm text-gray-900">{profile.name}</p>
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address</Label>
                      {isEditingEmail ? (
                        <div className="mt-1 space-y-2">
                          <Input
                            type="email"
                            value={newEmail}
                            onChange={(e) => setNewEmail(e.target.value)}
                            placeholder="Enter new email address"
                            className="w-full"
                          />
                          <div className="flex gap-2">
                            <Button size="sm" onClick={handleEmailChange}>
                              <i className="fas fa-check mr-1"></i>
                              Update Email
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setIsEditingEmail(false)
                                setNewEmail("")
                              }}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div className="mt-1 flex items-center gap-2">
                            <p className="text-sm text-gray-900">{profile.email}</p>
                            <Badge
                              variant={profile.email_verified ? "default" : "destructive"}
                              className={`text-xs ${
                                profile.email_verified ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                              }`}
                            >
                              {profile.email_verified ? (
                                <>
                                  <i className="fas fa-check-circle mr-1"></i>
                                  Verified
                                </>
                              ) : (
                                <>
                                  <i className="fas fa-exclamation-circle mr-1"></i>
                                  Unverified
                                </>
                              )}
                            </Badge>
                          </div>
                          <div className="mt-2 flex gap-2">
                            {!profile.email_verified && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="text-xs"
                                  onClick={handleResendVerification}
                                  disabled={isResending || remainingTime > 0}
                                >
                                  {isResending ? (
                                    <>
                                      <i className="fas fa-spinner fa-spin mr-1"></i>
                                      Sending...
                                    </>
                                  ) : remainingTime > 0 ? (
                                    <>
                                      <i className="fas fa-clock mr-1"></i>
                                      Resend in {remainingTime}s
                                    </>
                                  ) : (
                                    <>
                                      <i className="fas fa-envelope mr-1"></i>
                                      Resend Verification Email
                                    </>
                                  )}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="text-xs"
                                  onClick={() => {
                                    setIsEditingEmail(true)
                                    setNewEmail(profile.email)
                                  }}
                                >
                                  <i className="fas fa-edit mr-1"></i>
                                  Change Email
                                </Button>
                              </>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="membership">Membership Type</Label>
                      <p className="mt-1 text-sm text-gray-900">{profile.membership || "Not specified"}</p>
                    </div>
                    <div>
                      <Label htmlFor="country">Country</Label>
                      <p className="mt-1 text-sm text-gray-900">{profile.country || "Not specified"}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Professional Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <Label htmlFor="organization">Organization</Label>
                      {isEditing ? (
                        <Input
                          id="organization"
                          value={profile.organization || ""}
                          onChange={(e) => handleInputChange("organization", e.target.value)}
                          className="mt-1"
                        />
                      ) : (
                        <p className="mt-1 text-sm text-gray-900">{profile.organization || "Not specified"}</p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="position">Position</Label>
                      {isEditing ? (
                        <Select
                          value={profile.position || ""}
                          onValueChange={(value) => handleInputChange("position", value)}
                        >
                          <SelectTrigger id="position" className="mt-1">
                            <SelectValue placeholder="Select a position" />
                          </SelectTrigger>
                          <SelectContent>
                            {positions.map((position) => (
                              <SelectItem key={position.id} value={position.title}>
                                {position.title}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <p className="mt-1 text-sm text-gray-900">{profile.position || "Not specified"}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="bio">Biography</Label>
                    {isEditing ? (
                      <textarea
                        id="bio"
                        className="mt-1 w-full resize-none rounded-md border border-gray-300 p-3"
                        rows={4}
                        value={profile.bio || ""}
                        onChange={(e) => handleInputChange("bio", e.target.value)}
                        placeholder="Tell us about yourself and your research..."
                      />
                    ) : (
                      <p className="mt-1 text-sm leading-relaxed text-gray-900">
                        {profile.bio || "No biography provided"}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="expertise">Areas of Expertise</Label>
                    {isEditing ? (
                      <div className="mt-1">
                        <textarea
                          id="expertise"
                          value={expertiseInput}
                          onChange={(e) => {
                            // 直接保存用户输入的原始文本，包括逗号
                            const inputValue = e.target.value
                            setExpertiseInput(inputValue)

                            // 同时更新profile中的expertise数组，用于保存
                            if (inputValue.trim() === "") {
                              handleInputChange("expertise", [])
                            } else {
                              // 将文本按逗号分割为数组
                              const expertiseArray = inputValue
                                .split(",")
                                .map((item) => item.trim())
                                .filter((item) => item !== "")
                              handleInputChange("expertise", expertiseArray)
                            }
                          }}
                          className="mt-1 w-full resize-none rounded-md border border-gray-300 p-3"
                          rows={2}
                          placeholder="Enter expertise separated by commas"
                        />
                        <p className="mt-1 text-xs text-gray-500">Separate multiple areas of expertise with commas</p>
                      </div>
                    ) : (
                      <div className="mt-2 flex flex-wrap gap-2">
                        {profile.expertise?.length ? (
                          profile.expertise.map((skill, index) => (
                            <Badge key={`expertise-${index}-${skill}`} variant="secondary">
                              {skill}
                            </Badge>
                          ))
                        ) : (
                          <p className="text-sm text-gray-500">No expertise specified</p>
                        )}
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="interest">Research Interests</Label>
                    {isEditing ? (
                      <textarea
                        id="interest"
                        className="mt-1 w-full resize-none rounded-md border border-gray-300 p-3"
                        rows={3}
                        value={profile.interest || ""}
                        onChange={(e) => handleInputChange("interest", e.target.value)}
                        placeholder="Describe your research interests..."
                      />
                    ) : (
                      <p className="mt-1 text-sm leading-relaxed text-gray-900">
                        {profile.interest || "No research interests specified"}
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
