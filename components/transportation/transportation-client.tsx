"use client"

import { motion } from "framer-motion"

import Image from "next/image"
import { useState } from "react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
// import { generateAmapMarkerUrl, generateGoogleMapsUrl } from "@/utils/amap"

// Animation variants
const staggerContainer = (staggerChildren: number) => ({
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren,
    },
  },
})

const fadeIn = (direction: string, delay: number) => ({
  hidden: {
    opacity: 0,
    y: direction === "up" ? 40 : direction === "down" ? -40 : 0,
    x: direction === "left" ? 40 : direction === "right" ? -40 : 0,
  },
  show: {
    opacity: 1,
    y: 0,
    x: 0,
    transition: {
      duration: 0.6,
      delay,
      ease: "easeOut" as const,
    },
  },
})

type AmapLocation = {
  name: string
  viewOnMapName: string
  address: string
  coordinates: { lat: number; lng: number }
  type: string
}

type Venue = {
  name: string
  viewOnMapName: string
  address: string
  coordinates: { lat: number; lng: number }
}

type RoomType = {
  type: string
  price: number
}

type Hotel = {
  id: number
  name: string
  viewOnMapName: string
  address: string
  tel: string
  distance: string
  coordinates: { lat: number; lng: number }
  roomTypes: RoomType[]
}

type TransportOption = {
  mode: string
  details?: {
    distance: string
    time: string
    fare: string
  }
  steps?: string[]
}

type TransportRoute = {
  id: string
  from: string
  to: string
  options: TransportOption[]
}

type MapLocation = AmapLocation

type TransportationClientProps = {
  venue: Venue
  hotels: Hotel[]
  transportationRoutes: TransportRoute[]
}

export default function TransportationClient({ venue, hotels, transportationRoutes }: TransportationClientProps) {
  const [, setActiveTab] = useState("venue")
  const [isMapOpen, setIsMapOpen] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null)

  const handleViewOnMap = (location: MapLocation) => {
    setSelectedLocation(location)
    setIsMapOpen(true)
  }

  return (
    <>
      <main className="pb-20">
        {/* Hero Section */}
        <section className="relative overflow-hidden pt-32 pb-16 text-white">
          <div className="absolute inset-0">
            <Image
              src="https://minioapi.bugmaker.me/ifmb-2025-public/home-page.jpg"
              alt="Conference Background"
              fill
              priority
              className="object-cover object-top"
            />
            <div className="absolute inset-0 bg-black/30 backdrop-blur-sm"></div>
            <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-blue-900/30"></div>
            <div
              className="absolute inset-0 opacity-10"
              style={{ backgroundImage: `url('https://minioapi.bugmaker.me/ifmb-2025-public/pattern.svg')` }}
            ></div>
          </div>
          <div className="relative z-10 container mx-auto px-6">
            <div className="mx-auto max-w-3xl p-8 text-center">
              <Badge className="mb-4 border-blue-400/50 bg-blue-600/40 px-3 py-1 text-white">IFMB 2025</Badge>
              <h1 className="mb-6 text-4xl font-bold md:text-5xl">Transportation & Accommodation</h1>
              <p className="text-lg text-blue-100">
                Information about the conference venue, recommended hotels, and transportation routes to help you plan
                your trip to IFMB 2025
              </p>
            </div>
          </div>
        </section>

        {/* Tabs Navigation and Content */}
        <Tabs defaultValue="venue" className="w-full" onValueChange={setActiveTab}>
          {/* Navigation Tabs */}
          <section className="border-b bg-white py-8">
            <div className="container mx-auto px-6">
              <TabsList className="mx-auto grid w-full max-w-3xl grid-cols-1 gap-2 md:grid-cols-3">
                <TabsTrigger value="venue" className="px-4 py-2">
                  Conference Venue
                </TabsTrigger>
                <TabsTrigger value="hotels" className="px-4 py-2">
                  Hotels
                </TabsTrigger>
                <TabsTrigger value="routes" className="px-4 py-2">
                  Transportation Routes
                </TabsTrigger>
              </TabsList>
            </div>
          </section>

          {/* Content Sections */}
          <section className="bg-gray-50 py-12">
            <div className="container mx-auto px-6">
              <div className="mx-auto max-w-5xl">
                <TabsContent value="venue" className="mt-0">
                  <VenueSection venue={venue} onViewOnMap={handleViewOnMap} />
                </TabsContent>

                <TabsContent value="hotels" className="mt-0">
                  <HotelsSection hotels={hotels} onViewOnMap={handleViewOnMap} />
                </TabsContent>

                <TabsContent value="routes" className="mt-0">
                  <RoutesSection routes={transportationRoutes} />
                </TabsContent>
              </div>
            </div>
          </section>
        </Tabs>
      </main>

      {/* Map Modal */}
      {isMapOpen && selectedLocation && <MapModal location={selectedLocation} onClose={() => setIsMapOpen(false)} />}
    </>
  )
}

// Venue Section Component
function VenueSection({ venue, onViewOnMap }: { venue: Venue; onViewOnMap: (location: MapLocation) => void }) {
  return (
    <motion.div initial="hidden" whileInView="show" viewport={{ once: true }} variants={staggerContainer(0.1)}>
      <Card className="p-8 shadow-md">
        <motion.div variants={fadeIn("up", 0.1)}>
          <div className="mb-6 flex items-center">
            <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-50 text-green-700">
              <i className="fas fa-university text-xl"></i>
            </div>
            <h2 className="text-2xl font-bold text-gray-900">Conference Venue</h2>
          </div>

          <div className="mb-8">
            <h3 className="mb-2 text-xl font-semibold text-gray-800">{venue.name}</h3>
            <p className="mb-4 text-gray-600">{venue.address}</p>

            <div className="rounded-lg bg-gray-100 p-6">
              <div className="mb-4 flex items-start">
                <div className="mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-50 text-green-700">
                  <i className="fas fa-info-circle"></i>
                </div>
                <p className="text-gray-700">
                  The National Key Laboratory of Crop Genetic Improvement at Huazhong Agricultural University is one of
                  China's premier research facilities for crop genetics and breeding. The laboratory features
                  state-of-the-art equipment and facilities for genomics, phenomics, and other advanced research in crop
                  science.
                </p>
              </div>

              <div className="flex items-start">
                <div className="mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-50 text-green-700">
                  <i className="fas fa-map-marker-alt"></i>
                </div>
                <div>
                  <p className="mb-2 text-gray-700">
                    The laboratory is located on the campus of Huazhong Agricultural University in the Hongshan District
                    of Wuhan, a major educational and research hub in central China.
                  </p>
                  <Button
                    className="mt-2 bg-green-700 text-white hover:bg-green-800"
                    onClick={() =>
                      onViewOnMap({
                        name: venue.name,
                        viewOnMapName: venue.viewOnMapName,
                        address: venue.address,
                        coordinates: venue.coordinates,
                        type: "venue",
                      })
                    }
                  >
                    <i className="fas fa-map-marked-alt mr-2"></i> View on Map
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-lg bg-green-50 p-6">
            <h3 className="mb-3 flex items-center text-lg font-semibold text-gray-800">
              <i className="fas fa-lightbulb mr-2 text-amber-500"></i> Helpful Information
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <i className="fas fa-check-circle mt-1 mr-2 text-green-600"></i>
                <span className="text-gray-700">
                  The conference will take place in the main auditorium of the laboratory building.
                </span>
              </li>
              <li className="flex items-start">
                <i className="fas fa-check-circle mt-1 mr-2 text-green-600"></i>
                <span className="text-gray-700">
                  Registration desk will be open from 8:00 AM on the first day of the conference.
                </span>
              </li>
              <li className="flex items-start">
                <i className="fas fa-check-circle mt-1 mr-2 text-green-600"></i>
                <span className="text-gray-700">
                  Free Wi-Fi will be available throughout the venue for all conference attendees.
                </span>
              </li>
              <li className="flex items-start">
                <i className="fas fa-check-circle mt-1 mr-2 text-green-600"></i>
                <span className="text-gray-700">
                  Lunch and refreshments will be provided at the venue during the conference days.
                </span>
              </li>
            </ul>
          </div>
        </motion.div>
      </Card>
    </motion.div>
  )
}

// Hotels Section Component
function HotelsSection({ hotels, onViewOnMap }: { hotels: Hotel[]; onViewOnMap: (location: MapLocation) => void }) {
  return (
    <motion.div initial="hidden" whileInView="show" viewport={{ once: true }} variants={staggerContainer(0.1)}>
      <div className="mb-8">
        <motion.div variants={fadeIn("up", 0.1)}>
          <div className="mb-6 flex items-center">
            <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-50 text-green-700">
              <i className="fas fa-hotel text-xl"></i>
            </div>
            <h2 className="text-2xl font-bold text-gray-900">Recommended Hotels</h2>
          </div>

          <p className="mb-6 text-gray-600">
            We have arranged special rates at the following hotels for IFMB 2025 attendees. Please mention the
            conference name when making your reservation to receive the discounted rate.
          </p>
        </motion.div>
      </div>

      <div className="mb-10">
        <motion.div variants={fadeIn("up", 0.2)}>
          <Card className="overflow-hidden shadow-md">
            <div className="bg-green-700 px-6 py-4 text-white">
              <h3 className="text-lg font-semibold">Room Types & Price List</h3>
            </div>
            <div className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                        Hotel
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                        Room Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                        Price (CNY)
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {hotels.flatMap((hotel) =>
                      hotel.roomTypes.map((room, index) => (
                        <tr key={`${hotel.id}-${index}`} className="hover:bg-gray-50">
                          {index === 0 && (
                            <td className="px-6 py-4 align-top text-sm text-gray-900" rowSpan={hotel.roomTypes.length}>
                              {hotel.name}
                            </td>
                          )}
                          <td className="px-6 py-4 text-sm text-gray-500">{room.type}</td>
                          <td className="px-6 py-4 text-sm text-gray-900">{room.price}</td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {hotels.map((hotel, index) => (
          <motion.div key={hotel.id} variants={fadeIn("up", 0.1 * (index + 3))}>
            <Card className="p-6 shadow-md transition-shadow hover:shadow-lg">
              <div className="flex flex-col md:flex-row md:items-start">
                <div className="mb-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-green-50 text-green-700 md:mr-4 md:mb-0">
                  <i className="fas fa-building text-xl"></i>
                </div>
                <div className="flex-1">
                  <div className="mb-2 flex flex-col justify-between md:flex-row md:items-center">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-800">{hotel.name}</h3>
                      <p className="mt-1 text-sm text-gray-500">{hotel.viewOnMapName}</p>
                    </div>
                    <Badge className="mt-2 self-start bg-green-50 text-green-700 md:mt-0 md:self-auto">
                      {hotel.distance} from venue
                    </Badge>
                  </div>

                  <p className="mb-4 text-gray-600">{hotel.address}</p>

                  <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center">
                    <div className="flex items-center">
                      <i className="fas fa-phone mr-2 text-green-600"></i>
                      <span className="text-gray-700">{hotel.tel}</span>
                    </div>
                    <div className="flex items-center">
                      <i className="fas fa-bed mr-2 text-green-600"></i>
                      <span className="text-gray-700">{hotel.roomTypes.map((room) => room.type).join(", ")}</span>
                    </div>
                  </div>

                  <div className="flex flex-col gap-3 sm:flex-row">
                    <Button
                      className="bg-green-700 text-white hover:bg-green-800"
                      onClick={() =>
                        onViewOnMap({
                          name: hotel.name,
                          viewOnMapName: hotel.viewOnMapName,
                          address: hotel.address,
                          coordinates: hotel.coordinates,
                          type: "hotel",
                        })
                      }
                    >
                      <i className="fas fa-map-marker-alt mr-2"></i> View on Map
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

// Routes Section Component
function RoutesSection({ routes }: { routes: TransportRoute[] }) {
  return (
    <motion.div initial="hidden" whileInView="show" viewport={{ once: true }} variants={staggerContainer(0.1)}>
      <div className="mb-8">
        <motion.div variants={fadeIn("up", 0.1)}>
          <div className="mb-6 flex items-center">
            <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-50 text-green-700">
              <i className="fas fa-route text-xl"></i>
            </div>
            <h2 className="text-2xl font-bold text-gray-900">Transportation Routes</h2>
          </div>

          <p className="mb-6 text-gray-600">
            Below are the recommended transportation routes from major transportation hubs in Wuhan to the conference
            venue. Both taxi and public transport options are provided.
          </p>
        </motion.div>
      </div>

      <div className="space-y-8">
        {routes.map((route, index) => (
          <motion.div key={route.id} variants={fadeIn("up", 0.1 * (index + 2))}>
            <Card className="overflow-hidden shadow-md">
              <div className="bg-green-700 px-6 py-4 text-white">
                <h3 className="text-lg font-semibold">
                  Route {route.id}: {route.from} → {route.to}
                </h3>
              </div>

              <div className="p-6">
                <div className="space-y-6">
                  {route.options.map((option, optIndex) => (
                    <div
                      key={`${route.id}-${optIndex}`}
                      className="border-b border-gray-200 pb-6 last:border-0 last:pb-0"
                    >
                      <div className="mb-4 flex items-center">
                        <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-green-50 text-green-700">
                          <i className={`fas fa-${option.mode === "Taxi" ? "taxi" : "subway"}`}></i>
                        </div>
                        <h4 className="text-lg font-semibold text-gray-800">{option.mode}</h4>
                      </div>

                      {option.details && (
                        <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3">
                          <div className="rounded-lg bg-gray-50 p-3">
                            <div className="mb-1 text-sm text-gray-500">Distance</div>
                            <div className="font-medium text-gray-900">{option.details.distance}</div>
                          </div>
                          <div className="rounded-lg bg-gray-50 p-3">
                            <div className="mb-1 text-sm text-gray-500">Travel Time</div>
                            <div className="font-medium text-gray-900">{option.details.time}</div>
                          </div>
                          <div className="rounded-lg bg-gray-50 p-3">
                            <div className="mb-1 text-sm text-gray-500">Fare</div>
                            <div className="font-medium text-gray-900">{option.details.fare}</div>
                          </div>
                        </div>
                      )}

                      {option.steps && (
                        <div>
                          <h5 className="mb-3 text-sm font-medium text-gray-700">Step by Step Directions:</h5>
                          <ol className="list-decimal space-y-2 pl-6">
                            {option.steps.map((step, stepIndex) => (
                              <li key={stepIndex} className="text-gray-600">
                                {step}
                              </li>
                            ))}
                          </ol>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      <motion.div variants={fadeIn("up", 0.5)} className="mt-10">
        <Card className="border-amber-200 bg-amber-50 p-6">
          <div className="flex items-start">
            <div className="mr-4 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 text-amber-700">
              <i className="fas fa-lightbulb"></i>
            </div>
            <div>
              <h3 className="mb-2 text-lg font-semibold text-gray-800">Travel Tips</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <i className="fas fa-check-circle mt-1 mr-2 text-amber-600"></i>
                  <span className="text-gray-700">
                    We recommend using taxi services if you have heavy luggage or are unfamiliar with the city.
                  </span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-bus mt-1 mr-2 text-amber-600"></i>
                  <span className="text-gray-700">
                    Bus Route 590 (recommended for venue access) operates from 6:35 AM to 9:50 PM daily. Plan your
                    journey accordingly.
                  </span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check-circle mt-1 mr-2 text-amber-600"></i>
                  <span className="text-gray-700">
                    Mobile payment (WeChat Pay or Alipay) is widely accepted for public transportation and taxis in
                    Wuhan.
                  </span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check-circle mt-1 mr-2 text-amber-600"></i>
                  <span className="text-gray-700">
                    Consider downloading Baidu Maps or Amap (Gaode) for navigation in China, as Google Maps has limited
                    functionality.
                  </span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-ticket-alt mt-1 mr-2 text-amber-600"></i>
                  <span className="text-gray-700">
                    Bus fare is 2 CNY per trip. You can pay with exact change or use a Wuhan Transportation Card
                    available at metro stations.
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </Card>
      </motion.div>
    </motion.div>
  )
}

// Map Modal Component
function MapModal({ location, onClose }: { location: MapLocation; onClose: () => void }) {
  // 使用高德地图搜索功能显示地图
  const mapUrl = `https://www.amap.com/search?query=${encodeURIComponent(location.viewOnMapName)}&city=wuhan&zoom=15`
  // 生成标记URL
  const markerUrl = `https://www.amap.com/navi?dest=${location.coordinates.lng},${location.coordinates.lat}`
  // 生成Google Maps URL
  const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${location.coordinates.lat},${location.coordinates.lng}`

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm">
      <div className="relative max-h-[90vh] w-full max-w-6xl overflow-hidden rounded-lg bg-white shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between border-b bg-green-700 px-6 py-4 text-white">
          <div>
            <h3 className="text-lg font-semibold">{location.name}</h3>
            <p className="text-sm text-green-100">{location.address}</p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="text-white hover:bg-green-600">
            <i className="fas fa-times text-lg"></i>
          </Button>
        </div>

        {/* Map Content */}
        <div className="p-6">
          <div className="mb-4 rounded-lg bg-gray-50 p-4">
            <div className="mb-2 flex items-center">
              <i className="fas fa-map-marker-alt mr-2 text-green-600"></i>
              <span className="font-medium text-gray-800">Location Information</span>
            </div>
            <p className="mb-2 text-gray-600">{location.address}</p>
            <div className="text-sm text-gray-500">
              <span>Latitude: {location.coordinates.lat}</span>
              <span className="ml-4">Longitude: {location.coordinates.lng}</span>
            </div>
          </div>

          {/* Map iframe */}
          <div className="h-[500px] overflow-hidden rounded-lg border">
            <iframe
              src={mapUrl}
              width="100%"
              height="100%"
              frameBorder="0"
              title={`${location.viewOnMapName} Map`}
              className="h-full w-full"
              allow="geolocation"
              sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
            />
          </div>

          {/* Action buttons */}
          <div className="mt-4 flex flex-col gap-3 sm:flex-row sm:justify-center">
            <Button
              variant="outline"
              className="border-green-700 text-green-700 hover:bg-green-50"
              onClick={() => window.open(markerUrl, "_blank")}
            >
              <i className="fas fa-external-link-alt mr-2"></i> Open in Amap App
            </Button>
            <Button
              variant="outline"
              className="border-blue-600 text-blue-600 hover:bg-blue-50"
              onClick={() => window.open(googleMapsUrl, "_blank")}
            >
              <i className="fab fa-google mr-2"></i> View on Google Maps
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
