"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { useCallback, useState } from "react"
import TurnstileWidget from "@/components/auth/cloudflare-turnstile"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { sendForgotPasswordRequest } from "@/utils/users-api"

export default function ForgotPasswordForm() {
  const [turnstileToken, setTurnstileToken] = useState<string>("")
  const [email, setEmail] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [turnstileResetTrigger, setTurnstileResetTrigger] = useState(0)
  const [emailError, setEmailError] = useState("")

  const validateEmail = (value: string) => {
    let error = ""

    // 检查空格
    if (value.includes(" ")) {
      error = "Email address cannot contain spaces"
      return error
    }

    // 基本邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (value && !emailRegex.test(value)) {
      error = "Please enter a valid email address"
    }

    return error
  }

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setEmail(value)

    // 实时验证
    const error = validateEmail(value)
    setEmailError(error)
  }

  // 使用 useCallback 来稳定回调函数，避免不必要的重新渲染
  const handleTurnstileVerify = useCallback((token: string) => {
    setTurnstileToken(token)
  }, [])

  const handleTurnstileReset = useCallback(() => {
    setTurnstileToken("")
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email) {
      setError("Please enter your email address")
      return
    }

    // 验证邮箱不能包含空格
    if (email.includes(" ")) {
      setError("Email address cannot contain spaces")
      return
    }

    if (!turnstileToken) {
      setError("Please complete the verification")
      return
    }

    setLoading(true)
    setError("")

    try {
      await sendForgotPasswordRequest(email, turnstileToken)
      setSuccess(true)
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to send reset link. Please try again later.")

      // 重置Turnstile token，因为它已经被使用过了
      setTurnstileToken("")
      setTurnstileResetTrigger((prev) => prev + 1)
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 px-4 py-12">
        <div className="flex min-h-[calc(100vh-6rem)] items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="mx-auto w-full max-w-lg"
          >
            <Card className="border border-gray-200 bg-white text-center shadow-lg">
              <CardContent className="p-8">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-green-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <h1 className="mb-4 text-2xl font-bold text-gray-900">Check Your Email</h1>
                <p className="mb-6 leading-relaxed text-gray-600">
                  We've sent a password reset link to <span className="font-medium">{email}</span>. Please check your
                  email and follow the instructions to reset your password.
                </p>
                <Link href="/login">
                  <Button className="w-full bg-green-600 py-2 font-medium text-white hover:bg-green-700">
                    Return to Login
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 px-4 py-12">
      <div className="flex min-h-[calc(100vh-6rem)] items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mx-auto w-full max-w-lg"
        >
          <Card className="border border-gray-200 bg-white shadow-lg">
            <CardHeader className="pt-8 pb-6 text-center">
              <CardTitle className="mb-2 text-2xl font-bold text-gray-900">Forgot Password</CardTitle>
              <CardDescription className="text-gray-600">
                Enter your email address and we'll send you a link to reset your password
              </CardDescription>
            </CardHeader>

            <CardContent className="px-8 pt-6 pb-8">
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Email Field */}
                <div className="space-y-4">
                  <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Email Address</h3>

                  <div>
                    <label htmlFor="email" className="mb-1 block text-sm font-medium text-gray-700">
                      Email Address <span className="text-red-500">*</span>
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email address"
                      value={email}
                      onChange={handleEmailChange}
                      required
                      className={`w-full ${emailError ? "border-red-500" : ""}`}
                    />
                    {emailError && <p className="mt-1 text-xs text-red-500">{emailError}</p>}
                  </div>
                </div>

                {/* Verification */}
                <div className="space-y-4">
                  <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Verification</h3>
                  <div className="flex justify-center">
                    <TurnstileWidget
                      onVerify={handleTurnstileVerify}
                      onReset={handleTurnstileReset}
                      resetTrigger={turnstileResetTrigger}
                      theme="light"
                      language="en-US"
                    />
                  </div>
                </div>

                {/* Error message */}
                {error && (
                  <div className="rounded-md border border-red-200 bg-red-50 p-3 text-center">
                    <span className="text-sm text-red-600">{error}</span>
                  </div>
                )}

                {/* Submit button */}
                <div className="pt-4">
                  <Button
                    type="submit"
                    className="w-full bg-green-600 py-2 font-medium text-white hover:bg-green-700"
                    disabled={loading || !turnstileToken}
                  >
                    {loading ? "Sending..." : "Send Reset Link"}
                  </Button>

                  <p className="mt-4 text-center text-sm text-gray-600">
                    Remember your password?{" "}
                    <Link href="/login" className="font-medium text-green-600 hover:text-green-500">
                      Back to login
                    </Link>
                  </p>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
