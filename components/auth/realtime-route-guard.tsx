"use client"

import { usePathname, useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import AccessDeniedPage from "@/components/auth/access-denied-page"
import PageLoading from "@/components/layout/page-loading"
import { hasPermission, isPathInList, permissionConfig } from "@/config/permissions"
import { getCurrentUser, getCurrentUserFromAPI } from "@/utils/auth"

interface RealtimeRouteGuardProps {
  children: React.ReactNode
  customPermissions?: boolean // 是否使用自定义权限配置而不是默认配置
  showAccessDenied?: boolean // 是否显示访问拒绝页面而不是重定向
  forceRefresh?: boolean // 是否强制从API获取最新用户状态
}

type AccessDeniedReason = "not-logged-in" | "email-not-verified" | "not-admin" | "not-paid" | "insufficient-permissions"

export default function RealtimeRouteGuard({
  children,
  customPermissions: _customPermissions = false,
  showAccessDenied = true,
  forceRefresh = false,
}: RealtimeRouteGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [authorized, setAuthorized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [accessDeniedInfo, setAccessDeniedInfo] = useState<{
    reason: AccessDeniedReason
    userRole?: string
    userEmail?: string
    attemptedPath?: string
  } | null>(null)

  useEffect(() => {
    // 检查用户权限
    const checkPermissions = async () => {
      setIsLoading(true)

      try {
        // 获取当前路径
        const currentPath = pathname || ""

        // 如果是公开路径，直接允许访问
        if (isPathInList(currentPath, permissionConfig.publicPaths)) {
          setAuthorized(true)
          setAccessDeniedInfo(null)
          setIsLoading(false)
          return
        }

        // 从localStorage获取基本用户信息
        const localUser = getCurrentUser()

        // 默认为未登录状态
        let isLoggedIn = false
        let isAdmin = false
        let isEmailVerified = false
        let isPaid = false
        let userRole = "guest"
        let userEmail = ""

        // 如果有本地用户信息，先进行基本检查
        if (localUser) {
          isLoggedIn = true
          isAdmin = (localUser.role as string) === "admin"
          userRole = (localUser.role as string) || "user"
          userEmail = (localUser.email as string) || ""
          isEmailVerified = (localUser.email_verified as boolean) === true
          isPaid = (localUser.paid as boolean) === true
        }

        // 如果未登录，直接拒绝访问
        if (!isLoggedIn) {
          setAuthorized(false)
          if (showAccessDenied) {
            setAccessDeniedInfo({
              reason: "not-logged-in",
              userRole,
              userEmail,
              attemptedPath: currentPath,
            })
          } else {
            router.push("/login")
          }
          setIsLoading(false)
          return
        }

        // 对于需要付费的路径或强制刷新的情况，从API获取最新状态
        const needsRealtimeCheck =
          forceRefresh ||
          isPathInList(currentPath, permissionConfig.paidUserPaths) ||
          currentPath.startsWith("/dashboard/submission") ||
          currentPath.startsWith("/dashboard/accommodation")

        if (needsRealtimeCheck) {
          try {
            const apiUser = await getCurrentUserFromAPI()
            if (apiUser) {
              // 使用API返回的最新数据
              isEmailVerified = (apiUser.email_verified as boolean) === true
              isPaid = (apiUser.paid as boolean) === true
              userRole = (apiUser.role as string) || "user"
              userEmail = (apiUser.email as string) || ""
              isAdmin = (apiUser.role as string) === "admin"
            }
          } catch (error) {
            console.warn("Failed to fetch realtime user status, using cached data:", error)
            // 如果API调用失败，继续使用localStorage中的数据
          }
        }

        // 检查当前路径是否有权限访问
        const hasAccess = hasPermission(currentPath, isLoggedIn, isAdmin, isEmailVerified, isPaid)

        if (!hasAccess) {
          // 无权访问当前路径
          setAuthorized(false)

          if (showAccessDenied) {
            // 显示访问拒绝页面
            let reason: AccessDeniedReason = "insufficient-permissions"

            if (!isEmailVerified && !isAdmin) {
              reason = "email-not-verified"
              // 存储用户尝试访问的页面，以便验证后重定向
              if (typeof window !== "undefined") {
                if (!currentPath.startsWith("/dashboard") && currentPath !== "/profile") {
                  sessionStorage.setItem("redirectAfterVerification", currentPath)
                }
              }
            } else if (isLoggedIn && !isAdmin && currentPath.startsWith("/admin")) {
              reason = "not-admin"
            } else if (isLoggedIn && isEmailVerified && !isPaid && !isAdmin) {
              // 检查是否访问需要付费的页面
              if (isPathInList(currentPath, permissionConfig.paidUserPaths)) {
                reason = "not-paid"
              }
            }

            setAccessDeniedInfo({
              reason,
              userRole,
              userEmail,
              attemptedPath: currentPath,
            })
          } else {
            // 传统的重定向行为
            if (!isEmailVerified && !isAdmin) {
              if (typeof window !== "undefined") {
                if (!currentPath.startsWith("/dashboard") && currentPath !== "/profile") {
                  sessionStorage.setItem("redirectAfterVerification", currentPath)
                }
              }
              router.push("/dashboard")
            } else {
              router.push("/dashboard")
            }
          }
        } else {
          // 有权访问当前路径
          setAuthorized(true)
          setAccessDeniedInfo(null)
        }
      } catch (error) {
        console.error("Error checking permissions:", error)
        setAuthorized(false)
        setAccessDeniedInfo({
          reason: "insufficient-permissions",
          attemptedPath: pathname || "",
        })
      } finally {
        setIsLoading(false)
      }
    }

    // 首次加载时检查
    checkPermissions()

    // 监听路由变化
    const handleRouteChange = () => {
      checkPermissions()
    }

    // 监听localStorage变化（用户登录/登出）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "user") {
        checkPermissions()
      }
    }

    // 添加事件监听器
    window.addEventListener("popstate", handleRouteChange)
    window.addEventListener("storage", handleStorageChange)

    return () => {
      // 清理监听器
      window.removeEventListener("popstate", handleRouteChange)
      window.removeEventListener("storage", handleStorageChange)
    }
  }, [pathname, router, forceRefresh, showAccessDenied])

  // 显示加载状态、访问拒绝页面或已授权的内容
  if (isLoading) {
    return <PageLoading />
  }

  if (!authorized && accessDeniedInfo) {
    return (
      <AccessDeniedPage
        reason={accessDeniedInfo.reason}
        userRole={accessDeniedInfo.userRole}
        userEmail={accessDeniedInfo.userEmail}
        attemptedPath={accessDeniedInfo.attemptedPath}
      />
    )
  }

  return authorized ? <>{children}</> : null
}
