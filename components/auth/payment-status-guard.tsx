"use client"

import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import PageLoading from "@/components/layout/page-loading"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useRealtimeUserStatus } from "@/hooks/use-user-status"

interface PaymentStatusGuardProps {
  children: React.ReactNode
  showPaymentInfo?: boolean // 是否显示支付信息页面
  redirectTo?: string // 未支付时重定向的页面
}

/**
 * 支付状态守卫组件
 * 检查用户是否已支付，未支付时显示支付信息或重定向
 */
export default function PaymentStatusGuard({
  children,
  showPaymentInfo = true,
  redirectTo = "/dashboard/payment",
}: PaymentStatusGuardProps) {
  const router = useRouter()
  const { user, isLoading, hasPermission, refresh } = useRealtimeUserStatus()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkPaymentStatus = async () => {
      setIsChecking(true)

      // 如果没有用户信息，重定向到登录页
      if (!user) {
        router.push("/login")
        return
      }

      // 刷新用户状态以获取最新的支付信息
      try {
        await refresh()
      } catch (error) {
        console.error("Failed to refresh user status:", error)
      }

      setIsChecking(false)
    }

    if (!isLoading) {
      checkPaymentStatus()
    }
  }, [user, isLoading, refresh, router])

  // 显示加载状态
  if (isLoading || isChecking) {
    return <PageLoading />
  }

  // 用户未登录
  if (!user) {
    return null // 会被重定向到登录页
  }

  // 管理员用户直接通过
  if (hasPermission("admin")) {
    return <>{children}</>
  }

  // 用户已支付，显示内容
  if (hasPermission("paid")) {
    return <>{children}</>
  }

  // 用户未支付，显示支付信息页面或重定向
  if (!showPaymentInfo) {
    router.push(redirectTo)
    return <PageLoading />
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Payment Required</h1>
        <p className="mt-1 text-gray-600">You need to complete payment to access this feature</p>
      </div>

      <Card className="border-amber-200 bg-amber-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-amber-800">
              <i className="fas fa-exclamation-triangle mr-2"></i>
              Payment Required
            </CardTitle>
            <Badge variant="outline" className="border-amber-300 text-amber-700">
              Pending Payment
            </Badge>
          </div>
          <CardDescription className="text-amber-700">
            To access this feature, you need to complete your conference registration payment.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-white p-4">
            <h3 className="mb-2 font-semibold text-gray-900">What you get with payment:</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <i className="fas fa-check mr-2 text-green-500"></i>
                Abstract submission and management
              </li>
              <li className="flex items-center">
                <i className="fas fa-check mr-2 text-green-500"></i>
                Hotel accommodation booking
              </li>
              <li className="flex items-center">
                <i className="fas fa-check mr-2 text-green-500"></i>
                Conference materials and resources
              </li>
              <li className="flex items-center">
                <i className="fas fa-check mr-2 text-green-500"></i>
                Networking opportunities
              </li>
              <li className="flex items-center">
                <i className="fas fa-check mr-2 text-green-500"></i>
                Certificate of participation
              </li>
            </ul>
          </div>

          <div className="flex flex-col gap-3 sm:flex-row">
            <Button onClick={() => router.push("/dashboard/payment")} className="flex-1">
              <i className="fas fa-credit-card mr-2"></i>
              Complete Payment
            </Button>
            <Button variant="outline" onClick={() => router.push("/dashboard")} className="flex-1">
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Dashboard
            </Button>
          </div>

          <div className="text-center">
            <Button variant="ghost" size="sm" onClick={refresh} className="text-amber-700 hover:text-amber-800">
              <i className="fas fa-sync-alt mr-2"></i>
              Refresh Payment Status
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Payment Information</CardTitle>
          <CardDescription>Important details about conference payment</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start">
              <i className="fas fa-info-circle mt-1 mr-3 text-blue-500"></i>
              <div>
                <p className="font-medium">Registration Fees</p>
                <p className="text-sm text-gray-600">
                  Early bird registration is available until August 15, 2025. Regular registration fees apply after this
                  date.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <i className="fas fa-shield-alt mt-1 mr-3 text-blue-500"></i>
              <div>
                <p className="font-medium">Secure Payment</p>
                <p className="text-sm text-gray-600">
                  All payments are processed securely through our trusted payment partners. Your financial information
                  is protected.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <i className="fas fa-headset mt-1 mr-3 text-blue-500"></i>
              <div>
                <p className="font-medium">Need Help?</p>
                <p className="text-sm text-gray-600">
                  If you have any questions about payment or registration, please contact our support team.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
