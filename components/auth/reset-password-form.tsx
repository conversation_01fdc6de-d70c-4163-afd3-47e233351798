"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useCallback, useState } from "react"
import TurnstileWidget from "@/components/auth/cloudflare-turnstile"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { resetPassword } from "@/utils/users-api"

export default function ResetPasswordForm() {
  const [form, setForm] = useState({
    newPassword: "",
    confirmPassword: "",
  })
  const [turnstileToken, setTurnstileToken] = useState<string>("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [turnstileResetTrigger, setTurnstileResetTrigger] = useState(0)
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get("token")

  if (!token) {
    return (
      <div className="min-h-screen bg-gray-50 px-4 py-12">
        <div className="flex min-h-[calc(100vh-6rem)] items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="mx-auto w-full max-w-lg"
          >
            <Card className="border border-gray-200 bg-white text-center shadow-lg">
              <CardContent className="p-8">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-red-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <h1 className="mb-4 text-2xl font-bold text-gray-900">Invalid Reset Link</h1>
                <p className="mb-6 leading-relaxed text-gray-600">
                  The password reset link is invalid or has expired. Please request a new password reset link.
                </p>
                <Link href="/forgot-password">
                  <Button className="w-full bg-green-600 py-2 font-medium text-white hover:bg-green-700">
                    Request New Reset Link
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value })
  }

  // 使用 useCallback 来稳定回调函数，避免不必要的重新渲染
  const handleTurnstileVerify = useCallback((token: string) => {
    setTurnstileToken(token)
  }, [])

  const handleTurnstileReset = useCallback(() => {
    setTurnstileToken("")
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Form validation
    if (form.newPassword !== form.confirmPassword) {
      setError("Passwords do not match")
      return
    }

    if (form.newPassword.length < 8) {
      setError("Password must be at least 8 characters long")
      return
    }

    if (!turnstileToken) {
      setError("Please complete the verification")
      return
    }

    setLoading(true)
    setError("")

    try {
      await resetPassword(token, form.newPassword, turnstileToken)
      setSuccess(true)

      // Redirect to login page after 3 seconds
      setTimeout(() => {
        router.push("/login")
      }, 3000)
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to reset password. Please try again later.")

      // 重置Turnstile token，因为它已经被使用过了
      setTurnstileToken("")
      setTurnstileResetTrigger((prev) => prev + 1)
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 px-4 py-12">
        <div className="flex min-h-[calc(100vh-6rem)] items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="mx-auto w-full max-w-lg"
          >
            <Card className="border border-gray-200 bg-white text-center shadow-lg">
              <CardContent className="p-8">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-green-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h1 className="mb-4 text-2xl font-bold text-gray-900">Password Reset Successful</h1>
                <p className="mb-6 leading-relaxed text-gray-600">
                  Your password has been successfully reset. You will be redirected to the login page in a few seconds.
                </p>
                <Link href="/login">
                  <Button className="w-full bg-green-600 py-2 font-medium text-white hover:bg-green-700">
                    Go to Login
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 px-4 py-12">
      <div className="flex min-h-[calc(100vh-6rem)] items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mx-auto w-full max-w-lg"
        >
          <Card className="border border-gray-200 bg-white shadow-lg">
            <CardHeader className="pt-8 pb-6 text-center">
              <CardTitle className="mb-2 text-2xl font-bold text-gray-900">Reset Password</CardTitle>
              <CardDescription className="text-gray-600">Enter your new password below</CardDescription>
            </CardHeader>

            <CardContent className="px-8 pt-6 pb-8">
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="space-y-4">
                  <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">New Password</h3>

                  <div>
                    <label htmlFor="newPassword" className="mb-1 block text-sm font-medium text-gray-700">
                      New Password <span className="text-red-500">*</span>
                    </label>
                    <Input
                      id="newPassword"
                      name="newPassword"
                      type="password"
                      placeholder="Enter new password"
                      value={form.newPassword}
                      onChange={handleChange}
                      required
                      className="w-full"
                    />
                  </div>

                  <div>
                    <label htmlFor="confirmPassword" className="mb-1 block text-sm font-medium text-gray-700">
                      Confirm Password <span className="text-red-500">*</span>
                    </label>
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      placeholder="Confirm new password"
                      value={form.confirmPassword}
                      onChange={handleChange}
                      required
                      className="w-full"
                    />
                  </div>
                </div>

                {/* Verification */}
                <div className="space-y-4">
                  <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Verification</h3>
                  <div className="flex justify-center">
                    <TurnstileWidget
                      onVerify={handleTurnstileVerify}
                      onReset={handleTurnstileReset}
                      resetTrigger={turnstileResetTrigger}
                      theme="light"
                      language="en-US"
                    />
                  </div>
                </div>

                {/* Error message */}
                {error && (
                  <div className="rounded-md border border-red-200 bg-red-50 p-3 text-center">
                    <span className="text-sm text-red-600">{error}</span>
                  </div>
                )}

                {/* Submit button */}
                <div className="pt-4">
                  <Button
                    type="submit"
                    className="w-full bg-green-600 py-2 font-medium text-white hover:bg-green-700"
                    disabled={loading || !turnstileToken}
                  >
                    {loading ? "Resetting Password..." : "Reset Password"}
                  </Button>

                  <p className="mt-4 text-center text-sm text-gray-600">
                    Remember your password?{" "}
                    <Link href="/login" className="font-medium text-green-600 hover:text-green-500">
                      Back to login
                    </Link>
                  </p>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
