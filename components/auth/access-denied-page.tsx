"use client"

import Link from "next/link"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface AccessDeniedPageProps {
  reason: "not-logged-in" | "email-not-verified" | "not-admin" | "not-paid" | "insufficient-permissions"
  userRole?: string
  userEmail?: string
  attemptedPath?: string
}

export default function AccessDeniedPage({ reason, userRole, userEmail, attemptedPath }: AccessDeniedPageProps) {
  const router = useRouter()

  const getContent = () => {
    switch (reason) {
      case "not-logged-in":
        return {
          icon: "fas fa-sign-in-alt",
          iconColor: "text-blue-600",
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200",
          title: "Login Required",
          description: "You need to log in to access this page.",
          actions: (
            <div className="flex flex-col items-center gap-3 sm:flex-row sm:justify-center">
              <Link href="/login">
                <Button className="w-full sm:w-auto">
                  <i className="fas fa-sign-in-alt mr-2"></i>
                  Login Now
                </Button>
              </Link>
              <Link href="/register">
                <Button variant="outline" className="w-full sm:w-auto">
                  <i className="fas fa-user-plus mr-2"></i>
                  Register Account
                </Button>
              </Link>
            </div>
          ),
        }

      case "email-not-verified":
        return {
          icon: "fas fa-envelope-open-text",
          iconColor: "text-gray-600",
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200",
          title: "Email Verification Required",
          description: (
            <div>
              <p className="mb-3">You need to verify your email to access this page.</p>
              {userEmail && (
                <p className="text-sm text-gray-600">
                  Verification email sent to: <strong>{userEmail}</strong>
                </p>
              )}
              <p className="mt-2 text-sm text-gray-600">
                Please check your email and click the verification link. If you don't receive the email, please check
                your spam folder.
              </p>
            </div>
          ),
          actions: (
            <div className="flex flex-col items-center gap-3 sm:flex-row sm:justify-center">
              <Link href="/dashboard">
                <Button className="w-full sm:w-auto">
                  <i className="fas fa-tachometer-alt mr-2"></i>
                  Back to Dashboard
                </Button>
              </Link>
              <Button variant="outline" onClick={() => window.location.reload()} className="w-full sm:w-auto">
                <i className="fas fa-sync-alt mr-2"></i>
                Recheck
              </Button>
            </div>
          ),
        }

      case "not-admin":
        return {
          icon: "fas fa-user-shield",
          iconColor: "text-red-600",
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
          title: "Administrator Access Required",
          description: (
            <div>
              <p className="mb-3">This page is restricted to administrators only.</p>
              {userRole && (
                <p className="text-sm text-gray-600">
                  Your current role is: <strong>{userRole}</strong>
                </p>
              )}
              <p className="mt-2 text-sm text-gray-600">
                If you believe this is an error, please contact the system administrator.
              </p>
            </div>
          ),
          actions: (
            <div className="flex flex-col items-center gap-3 sm:flex-row sm:justify-center">
              <Link href="/dashboard">
                <Button className="w-full sm:w-auto">
                  <i className="fas fa-tachometer-alt mr-2"></i>
                  Back to Dashboard
                </Button>
              </Link>
              <Button variant="outline" onClick={() => router.back()} className="w-full sm:w-auto">
                <i className="fas fa-arrow-left mr-2"></i>
                Go Back
              </Button>
            </div>
          ),
        }

      case "not-paid":
        return {
          icon: "fas fa-credit-card",
          iconColor: "text-yellow-600",
          bgColor: "bg-yellow-50",
          borderColor: "border-yellow-200",
          title: "Payment Required",
          description: (
            <div>
              <p className="mb-3">You need to complete your conference payment to access this feature.</p>
              <p className="text-sm text-gray-600">
                This page is only available to participants who have completed their registration payment.
              </p>
              <p className="mt-2 text-sm text-gray-600">
                Please complete your payment to unlock all conference features including abstract submission and
                accommodation booking.
              </p>
            </div>
          ),
          actions: (
            <div className="flex flex-col items-center gap-3 sm:flex-row sm:justify-center">
              <Link href="/dashboard/payment">
                <Button className="w-full sm:w-auto">
                  <i className="fas fa-credit-card mr-2"></i>
                  Complete Payment
                </Button>
              </Link>
              <Link href="/dashboard">
                <Button variant="outline" className="w-full sm:w-auto">
                  <i className="fas fa-tachometer-alt mr-2"></i>
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          ),
        }

      default:
        return {
          icon: "fas fa-exclamation-triangle",
          iconColor: "text-orange-600",
          bgColor: "bg-orange-50",
          borderColor: "border-orange-200",
          title: "Access Restricted",
          description: "You do not have permission to access this page.",
          actions: (
            <div className="flex flex-col items-center gap-3 sm:flex-row sm:justify-center">
              <Link href="/dashboard">
                <Button className="w-full sm:w-auto">
                  <i className="fas fa-tachometer-alt mr-2"></i>
                  Back to Dashboard
                </Button>
              </Link>
              <Button variant="outline" onClick={() => router.back()} className="w-full sm:w-auto">
                <i className="fas fa-arrow-left mr-2"></i>
                Go Back
              </Button>
            </div>
          ),
        }
    }
  }

  const content = getContent()

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12">
      <Card className="w-full max-w-md border-gray-200 bg-white">
        <CardHeader className="text-center">
          <div className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full ${content.bgColor}`}>
            <i className={`${content.icon} ${content.iconColor} text-2xl`}></i>
          </div>
          <CardTitle className="text-xl font-bold text-gray-900">{content.title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center text-gray-600">{content.description}</div>

          {attemptedPath && (
            <div className="rounded-md bg-gray-100 p-3">
              <p className="text-sm text-gray-600">
                <strong>Attempted to access:</strong>
                <code className="ml-1 rounded bg-gray-200 px-1 py-0.5 text-xs">{attemptedPath}</code>
              </p>
            </div>
          )}

          <div className="pt-4">{content.actions}</div>

          <div className="border-t pt-4 text-center">
            <p className="text-xs text-gray-500">For assistance, please contact technical support</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
