"use client"
import Script from "next/script"
import { useCallback, useEffect, useRef } from "react"
import { env } from "@/env.mjs"

// 检查是否为开发环境
const isDevelopment = process.env.NODE_ENV === "development"

// 为Turnstile添加类型声明
declare global {
  interface Window {
    turnstile?: {
      render: (
        element: HTMLElement,
        options: {
          sitekey: string
          callback: (token: string) => void
          theme?: "light" | "dark"
          language?: string
        }
      ) => string
      remove: (element: HTMLElement) => void
      reset: (element: HTMLElement) => void
    }
  }
}

interface TurnstileWidgetProps {
  onVerify: (token: string) => void
  onReset?: () => void
  theme?: "light" | "dark"
  language?: string
  resetTrigger?: number // 用于触发重置的计数器
}

const TurnstileWidget: React.FC<TurnstileWidgetProps> = ({
  onVerify,
  onReset,
  theme = "dark",
  language = "en-US",
  resetTrigger = 0,
}) => {
  const divRef = useRef<HTMLDivElement>(null)
  const widgetIdRef = useRef<string | null>(null)

  // 使用 useRef 来存储 onVerify 回调，避免不必要的重新渲染
  const onVerifyRef = useRef(onVerify)
  useEffect(() => {
    onVerifyRef.current = onVerify
  }, [onVerify])

  // 在开发环境中自动提供一个假的token
  useEffect(() => {
    if (isDevelopment) {
      // 延迟一点时间模拟真实的验证过程
      const timer = setTimeout(() => {
        onVerifyRef.current("dev-bypass-token")
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [resetTrigger]) // 只依赖 resetTrigger，使用 ref 调用 onVerify

  const renderWidget = useCallback(() => {
    if (divRef.current && window.turnstile) {
      const sitekey = env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || ""
      if (!sitekey) {
        console.error("Turnstile site key is not configured")
        return
      }

      // 清除之前的widget
      if (widgetIdRef.current) {
        try {
          window.turnstile.remove(divRef.current)
        } catch (error) {
          console.warn("Failed to remove previous Turnstile widget:", error)
        }
        widgetIdRef.current = null
      }

      // 渲染新的widget
      try {
        widgetIdRef.current = window.turnstile.render(divRef.current, {
          sitekey,
          callback: (token: string) => {
            onVerifyRef.current(token)
          },
          theme,
          language,
        })
      } catch (error) {
        console.error("Failed to render Turnstile widget:", error)
      }
    }
  }, [theme, language]) // 移除 onVerify 依赖
  // 初始化 widget
  useEffect(() => {
    // 只在 turnstile 可用时渲染
    if (window.turnstile) {
      renderWidget()
    }
  }, [renderWidget])

  // 清理函数：组件卸载时移除 widget
  useEffect(() => {
    const divElement = divRef.current
    return () => {
      if (divElement && window.turnstile && widgetIdRef.current) {
        try {
          window.turnstile.remove(divElement)
        } catch (error) {
          console.warn("Failed to remove Turnstile widget on cleanup:", error)
        }
        widgetIdRef.current = null
      }
    }
  }, [])

  // 处理重置触发器
  useEffect(() => {
    if (resetTrigger > 0 && !isDevelopment) {
      if (divRef.current && window.turnstile) {
        try {
          window.turnstile.reset(divRef.current)
          onReset?.()
        } catch (error) {
          console.warn("Failed to reset Turnstile widget:", error)
          // 如果重置失败，尝试重新渲染
          renderWidget()
        }
      }
    }
  }, [resetTrigger, onReset, renderWidget])

  // 在开发环境中显示一个简化的界面
  if (isDevelopment) {
    return (
      <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-4">
        <div className="text-center">
          <div className="mb-2 text-sm font-medium text-gray-600">Development Mode</div>
          <div className="text-xs text-gray-500">Turnstile verification bypassed</div>
          <div className="mt-2 flex items-center justify-center">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-green-500 border-t-transparent"></div>
            <span className="ml-2 text-xs text-green-600">Auto-verifying...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <Script src="https://challenges.cloudflare.com/turnstile/v0/api.js" onLoad={renderWidget} />
      <div ref={divRef} />
    </>
  )
}

export default TurnstileWidget
