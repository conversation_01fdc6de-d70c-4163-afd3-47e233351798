"use client"

import React, { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface EmailVerificationBannerProps {
  userEmail?: string
}

export default function EmailVerificationBanner({ userEmail }: EmailVerificationBannerProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [lastSentTime, setLastSentTime] = useState<number | null>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient) return

    // 检查用户是否已验证邮箱
    const userData = localStorage.getItem("user")
    if (userData) {
      try {
        const parsedData = JSON.parse(userData) as Record<string, unknown>
        const userInfo = parsedData.user_info || (parsedData.data as Record<string, unknown>)?.user_info || parsedData
        const userInfoTyped = userInfo as Record<string, unknown>
        const isEmailVerified = (userInfoTyped.email_verified as boolean) === true
        const isAdmin = (userInfoTyped.role as string) === "admin"

        // 只对未验证邮箱的非管理员用户显示横幅
        setIsVisible(!isEmailVerified && !isAdmin)
      } catch (error) {
        console.error("Error parsing user data:", error)
      }
    }
  }, [isClient])

  const handleResendVerification = async () => {
    if (!userEmail) return

    setIsResending(true)
    try {
      // 这里应该调用重发验证邮件的API
      // const response = await fetch('/api/auth/resend-verification', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ email: userEmail })
      // })

      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setLastSentTime(Date.now())
      // 这里可以显示成功提示
    } catch (error) {
      console.error("Failed to resend verification email:", error)
      // 这里可以显示错误提示
    } finally {
      setIsResending(false)
    }
  }

  const canResend = !lastSentTime || Date.now() - lastSentTime > 60000 // 1分钟后可以重发

  // 在客户端渲染之前不显示任何内容，避免hydration错误
  if (!isClient || !isVisible) return null

  return (
    <Card className="mb-6 border-gray-200 bg-gray-50">
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <i className="fas fa-exclamation-triangle text-lg text-gray-600"></i>
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-800">Email Verification Required</h3>
            <p className="mt-1 text-sm text-gray-700">
              Your email address has not been verified yet. Please check your email and click the verification link to
              access all features.
              {userEmail && (
                <span className="mt-1 block">
                  Verification email was sent to: <strong>{userEmail}</strong>
                </span>
              )}
            </p>
            <div className="mt-3 flex flex-col items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleResendVerification}
                disabled={isResending || !canResend}
                className="border-gray-300 text-gray-800 hover:bg-gray-100"
              >
                {isResending ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Sending...
                  </>
                ) : (
                  <>
                    <i className="fas fa-envelope mr-2"></i>
                    {canResend ? "Resend Verification Email" : "Email Sent"}
                  </>
                )}
              </Button>
              {lastSentTime && (
                <span className="text-xs text-gray-600">
                  Email sent {Math.floor((Date.now() - lastSentTime) / 1000)}s ago
                </span>
              )}
            </div>
          </div>
          <div className="flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="text-gray-600 hover:bg-gray-100 hover:text-gray-800"
            >
              <i className="fas fa-times"></i>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
