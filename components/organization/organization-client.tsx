"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

import { fadeIn, staggerContainer } from "@/lib/animations"

// Types
type Person = {
  name: string
  title: string
  affiliation: string
  photo?: string
}

type ConferenceInfo = {
  title: string
  shortTitle: string
  dates: string
  location: string
  description?: string
}

type OrganizationClientProps = {
  chairmen: Person[]
  secretaryGeneral: Person
  internationalMembers: Person[]
  chineseMembers: Person[]
  conferenceInfo: ConferenceInfo
}

export default function OrganizationClient({
  chairmen,
  secretaryGeneral,
  internationalMembers,
  chineseMembers,
  conferenceInfo,
}: OrganizationClientProps) {
  return (
    <main className="pb-20">
      {/* Hero Section */}
      <section className="relative overflow-hidden pt-32 pb-16 text-white">
        <div className="absolute inset-0">
          <Image
            src="https://minioapi.bugmaker.me/ifmb-2025-public/home-page.jpg"
            alt="Conference Background"
            fill
            priority
            className="object-cover object-top"
          />
          <div className="absolute inset-0 bg-black/30 backdrop-blur-sm"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-blue-900/30"></div>
          <div
            className="absolute inset-0 opacity-10"
            style={{ backgroundImage: `url('https://minioapi.bugmaker.me/ifmb-2025-public/pattern.svg')` }}
          ></div>
        </div>
        <div className="relative z-10 container mx-auto px-6">
          <div className="mx-auto max-w-3xl p-8 text-center">
            <Badge className="mb-4 border-blue-400/50 bg-blue-600/40 px-3 py-1 text-white">
              {conferenceInfo.shortTitle}
            </Badge>
            <h1 className="mb-6 text-4xl font-bold md:text-5xl">Organization Committee</h1>
            <p className="text-lg text-blue-100">
              Meet the distinguished committee members leading the International Forum on Maize Biology 2025
            </p>
          </div>
        </div>
      </section>

      {/* Chairmen Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-6xl"
          >
            <div className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Chairmen</h2>
              <p className="mx-auto max-w-3xl text-gray-600">
                Leading the organization committee with expertise and vision.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              {chairmen.map((chair, index) => (
                <motion.div
                  key={chair.name}
                  variants={fadeIn("up", 0.1 * (index + 1))}
                  className="flex flex-col items-center"
                >
                  <Card className="h-full w-full p-8 text-center transition-shadow hover:shadow-lg">
                    <div className="mb-6">
                      <Avatar className="mx-auto h-32 w-32 border-4 border-green-100">
                        {chair.photo && (
                          <AvatarImage
                            src={
                              chair.photo.startsWith("http")
                                ? chair.photo
                                : `https://minioapi.bugmaker.me/ifmb-2025-public/organization/${chair.photo}`
                            }
                            alt={chair.name}
                            className="object-cover"
                          />
                        )}
                        <AvatarFallback>
                          {chair.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                    <h3 className="mb-2 text-xl font-bold text-gray-900">{chair.name}</h3>
                    <p className="mb-2 font-medium text-green-600">{chair.title}</p>
                    <p className="text-sm text-gray-600">{chair.affiliation}</p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Secretary-General Section */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-5xl"
          >
            <div className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Secretary-General</h2>
              <p className="mx-auto max-w-3xl text-gray-600">Coordinating the organization of IFMB 2025.</p>
            </div>

            <motion.div variants={fadeIn("up", 0.2)} className="flex flex-col items-center">
              <Card className="mx-auto max-w-2xl p-8 text-center transition-shadow hover:shadow-lg">
                <div className="mb-6">
                  <Avatar className="mx-auto h-40 w-40 border-4 border-green-100">
                    {secretaryGeneral.photo && (
                      <AvatarImage
                        src={
                          secretaryGeneral.photo.startsWith("http")
                            ? secretaryGeneral.photo
                            : `https://minioapi.bugmaker.me/ifmb-2025-public/organization/${secretaryGeneral.photo}`
                        }
                        alt={secretaryGeneral.name}
                        className="object-cover"
                      />
                    )}
                    <AvatarFallback>
                      {secretaryGeneral.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                </div>
                <h3 className="mb-2 text-2xl font-bold text-gray-900">{secretaryGeneral.name}</h3>
                <p className="mb-2 font-medium text-green-600">{secretaryGeneral.title}</p>
                <p className="text-gray-600">{secretaryGeneral.affiliation}</p>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* International Committee Members Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-6xl">
            <div className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">International and Journal Members</h2>
              <p className="mx-auto max-w-3xl text-gray-600">
                Distinguished experts from leading international institutions and journals.
              </p>
              <p className="mt-2 text-sm text-gray-500 italic">Sorted alphabetically by last name (A-Z)</p>
            </div>

            <motion.div
              initial="hidden"
              whileInView="show"
              viewport={{ once: true }}
              variants={staggerContainer(0.05)}
              className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
            >
              {internationalMembers.map((member, index) => (
                <MemberCard key={member.name} member={member} index={index} />
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Chinese Committee Members Section */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-6xl">
            <div className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Chinese Members</h2>
              <p className="mx-auto max-w-3xl text-gray-600">
                Leading researchers and scientists from Chinese institutions.
              </p>
              <p className="mt-2 text-sm text-gray-500 italic">Sorted alphabetically by last name (A-Z)</p>
            </div>

            <motion.div
              initial="hidden"
              whileInView="show"
              viewport={{ once: true }}
              variants={staggerContainer(0.05)}
              className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
            >
              {chineseMembers.map((member, index) => (
                <MemberCard key={`${member.name}-${index}`} member={member} index={index} />
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-green-50 py-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="mb-6 text-3xl font-bold text-gray-900">Join Us at IFMB 2025</h2>
            <p className="mb-8 text-lg text-gray-600">
              Be part of this groundbreaking event and connect with the leading experts in maize biology from around the
              world.
            </p>
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Link href="/register">
                <Button className="transform rounded-lg bg-green-600 px-8 py-4 text-lg font-semibold text-white shadow-lg transition-all duration-300 hover:-translate-y-1 hover:bg-green-700">
                  Register Now
                </Button>
              </Link>
              <Link href="/reporters">
                <Button
                  variant="outline"
                  className="transform rounded-lg border-2 border-green-600 bg-white px-8 py-4 text-lg font-semibold text-green-700 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:bg-green-50"
                >
                  View Speakers
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

// Member Card Component
type MemberCardProps = {
  member: Person
  index: number
}

function MemberCard({ member, index }: MemberCardProps) {
  return (
    <motion.div variants={fadeIn("up", 0.05 * (index + 1))}>
      <Card className="flex h-full flex-col p-6 transition-shadow hover:shadow-md">
        <div className="flex items-start space-x-4">
          <Avatar className="h-12 w-12 flex-shrink-0 rounded-full bg-green-100 text-green-600">
            <AvatarFallback>
              {member.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <h4 className="text-lg font-bold text-gray-900">{member.name}</h4>
            <p className="text-sm text-green-600">{member.title}</p>
            <p className="mt-1 text-sm text-gray-500">{member.affiliation}</p>
          </div>
        </div>
      </Card>
    </motion.div>
  )
}
