"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { fadeIn, staggerContainer } from "@/lib/animations"

// Types
type ConferenceInfo = {
  title: string
  shortTitle: string
  dates: string
  location: string
  venue: string
  websiteUrl: string
}

type Event = {
  time: string
  title: string
  type: string
  location: string
  description: string
}

type ScheduleDay = {
  date: string
  dayName: string
  title: string
  events: Event[]
}

type ScheduleClientProps = {
  conferenceInfo: ConferenceInfo
  scheduleData: ScheduleDay[]
}

// Event type styling
const eventTypeStyles = {
  registration: {
    icon: "user-check",
    color: "bg-blue-100 text-blue-800 border-blue-200",
    iconColor: "text-blue-600 bg-blue-100",
  },
  keynote: {
    icon: "microphone",
    color: "bg-purple-100 text-purple-800 border-purple-200",
    iconColor: "text-purple-600 bg-purple-100",
  },
  session: {
    icon: "users",
    color: "bg-green-100 text-green-800 border-green-200",
    iconColor: "text-green-600 bg-green-100",
  },
  break: {
    icon: "coffee",
    color: "bg-amber-100 text-amber-800 border-amber-200",
    iconColor: "text-amber-600 bg-amber-100",
  },
  meal: {
    icon: "utensils",
    color: "bg-orange-100 text-orange-800 border-orange-200",
    iconColor: "text-orange-600 bg-orange-100",
  },
  poster: {
    icon: "presentation",
    color: "bg-teal-100 text-teal-800 border-teal-200",
    iconColor: "text-teal-600 bg-teal-100",
  },
  meeting: {
    icon: "handshake",
    color: "bg-gray-100 text-gray-800 border-gray-200",
    iconColor: "text-gray-600 bg-gray-100",
  },
  departure: {
    icon: "plane-departure",
    color: "bg-red-100 text-red-800 border-red-200",
    iconColor: "text-red-600 bg-red-100",
  },
}

export default function ScheduleClient({ conferenceInfo, scheduleData }: ScheduleClientProps) {
  const [selectedDay, setSelectedDay] = useState(scheduleData[0]?.date || "")

  return (
    <main className="pb-20">
      {/* Hero Section */}
      <section className="relative overflow-hidden pt-32 pb-16 text-white">
        <div className="absolute inset-0">
          <Image
            src="https://minioapi.bugmaker.me/ifmb-2025-public/home-page.jpg"
            alt="Conference Background"
            fill
            priority
            className="object-cover object-top"
          />
          <div className="absolute inset-0 bg-black/30 backdrop-blur-sm"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-green-900/30"></div>
          <div
            className="absolute inset-0 opacity-10"
            style={{ backgroundImage: `url('https://minioapi.bugmaker.me/ifmb-2025-public/pattern.svg')` }}
          ></div>
        </div>
        <div className="relative z-10 container mx-auto px-6">
          <div className="mx-auto max-w-4xl p-8 text-center">
            <Badge className="mb-4 border-green-400/50 bg-green-600/40 px-3 py-1 text-white backdrop-blur-sm">
              {conferenceInfo.shortTitle}
            </Badge>
            <h1 className="mb-6 text-4xl font-bold md:text-5xl">Conference Schedule</h1>
            <p className="mb-8 text-lg text-green-100">
              Detailed program for {conferenceInfo.title} featuring keynote speeches, research sessions, and networking
              opportunities
            </p>
            <div className="flex flex-col items-center gap-4 sm:flex-row sm:justify-center">
              <div className="flex items-center gap-2 text-yellow-200">
                <i className="fas fa-calendar-alt"></i>
                <span className="font-medium">{conferenceInfo.dates}</span>
              </div>
              <div className="flex items-center gap-2 text-yellow-200">
                <i className="fas fa-map-marker-alt"></i>
                <span className="font-medium">{conferenceInfo.venue}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Schedule Content */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-6xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Daily Program</h2>
              <p className="text-lg text-gray-600">
                Five days of cutting-edge research presentations and networking opportunities
              </p>
            </motion.div>

            <Tabs value={selectedDay} onValueChange={setSelectedDay} className="w-full">
              <TabsList className="mb-8 grid w-full grid-cols-5">
                {scheduleData.map((day) => (
                  <TabsTrigger key={day.date} value={day.date} className="text-sm">
                    <div className="text-center">
                      <div className="font-medium">{day.dayName}</div>
                      <div className="text-xs text-gray-500">
                        {new Date(day.date).toLocaleDateString("en-US", { month: "short", day: "numeric" })}
                      </div>
                    </div>
                  </TabsTrigger>
                ))}
              </TabsList>

              {scheduleData.map((day) => (
                <TabsContent key={day.date} value={day.date}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="mb-6">
                      <CardHeader className="text-center">
                        <CardTitle className="text-2xl text-green-700">{day.title}</CardTitle>
                        <p className="text-gray-600">
                          {day.dayName},{" "}
                          {new Date(day.date).toLocaleDateString("en-US", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                        </p>
                      </CardHeader>
                    </Card>

                    <div className="space-y-4">
                      {day.events.map((event, index) => {
                        const eventStyle =
                          eventTypeStyles[event.type as keyof typeof eventTypeStyles] || eventTypeStyles.session
                        return (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.1 }}
                          >
                            <Card className="transition-shadow hover:shadow-md">
                              <CardContent className="p-6">
                                <div className="flex items-start gap-4">
                                  <div className="flex-shrink-0">
                                    <div
                                      className={`flex h-12 w-12 items-center justify-center rounded-lg ${eventStyle.iconColor}`}
                                    >
                                      <i className={`fas fa-${eventStyle.icon} text-lg`}></i>
                                    </div>
                                  </div>
                                  <div className="min-w-0 flex-1">
                                    <div className="mb-2 flex items-start justify-between gap-4">
                                      <div>
                                        <h3 className="mb-1 text-lg font-semibold text-gray-900">{event.title}</h3>
                                        <Badge className={`text-xs ${eventStyle.color}`}>
                                          {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                                        </Badge>
                                      </div>
                                      <div className="flex-shrink-0 text-right">
                                        <div className="text-lg font-bold text-green-600">{event.time}</div>
                                      </div>
                                    </div>
                                    {event.location && (
                                      <div className="mb-2 flex items-center gap-2 text-sm text-gray-600">
                                        <i className="fas fa-map-marker-alt"></i>
                                        <span>{event.location}</span>
                                      </div>
                                    )}
                                    <p className="text-sm leading-relaxed text-gray-700">{event.description}</p>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </motion.div>
                        )
                      })}
                    </div>
                  </motion.div>
                </TabsContent>
              ))}
            </Tabs>
          </motion.div>
        </div>
      </section>

      {/* Important Notes */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Important Information</h2>
              <p className="text-lg text-gray-600">Key details about the conference schedule</p>
            </motion.div>

            <div className="grid gap-6 md:grid-cols-2">
              <motion.div variants={fadeIn("up", 0.2)}>
                <Card className="h-full border-blue-200 bg-blue-50 p-6">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center text-blue-700">
                      <i className="fas fa-info-circle mr-3"></i>
                      Venue Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div>
                        <strong>NKLCGI:</strong> National Key Laboratory of Crop Genetic Improvement
                      </div>
                      <div>
                        <strong>IAEC:</strong> International Academic Exchange Center
                      </div>
                      <div>
                        <strong>Address:</strong> Huazhong Agricultural University, Wuhan, China
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={fadeIn("up", 0.3)}>
                <Card className="h-full border-green-200 bg-green-50 p-6">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center text-green-700">
                      <i className="fas fa-clock mr-3"></i>
                      Schedule Notes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div>• All times are in China Standard Time (CST)</div>
                      <div>• Tea breaks include networking opportunities</div>
                      <div>• Poster sessions run parallel with dinner</div>
                      <div>• Schedule subject to minor changes</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-green-50 py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl text-center"
          >
            <motion.div variants={fadeIn("up", 0.1)}>
              <h2 className="mb-6 text-3xl font-bold text-gray-900">Join Us at IFMB 2025</h2>
              <p className="mb-8 text-lg text-gray-600">
                Don't miss this opportunity to connect with leading researchers and discover the latest advances in
                maize biology.
              </p>
              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <Link href="/registration">
                  <Button className="bg-green-600 px-8 py-3 text-lg text-white hover:bg-green-700">
                    <i className="fas fa-user-plus mr-2"></i>
                    Register Now
                  </Button>
                </Link>
                <Link href="/abstract-submission">
                  <Button
                    variant="outline"
                    className="border-green-600 px-8 py-3 text-lg text-green-600 hover:bg-green-50"
                  >
                    <i className="fas fa-paper-plane mr-2"></i>
                    Submit Abstract
                  </Button>
                </Link>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </main>
  )
}
