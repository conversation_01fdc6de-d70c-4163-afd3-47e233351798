"use client"

import { useEffect, useState } from "react"
import { useUser } from "@/components/layout/user-context"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"

import { useToast } from "@/components/ui/toast"
import { getAuthToken } from "@/utils/auth"
import { getDocumentUrl } from "@/utils/minio"

// 从文件名提取原始文件名
const getOriginalFileName = (filePath: string) => {
  if (!filePath) return "Unknown File"

  // 从URL中提取文件名
  const urlParts = filePath.split("/")
  const fileName = urlParts[urlParts.length - 1]

  if (!fileName) return "Unknown File"

  // 移除URL参数
  const cleanFileName = fileName.split("?")[0]

  if (!cleanFileName) return "Unknown File"

  // URL解码文件名
  const decodedFileName = decodeURIComponent(cleanFileName)

  // 尝试提取原始文件名（移除时间戳等后缀）
  const match = decodedFileName.match(/^(.+?)_user_\d+_\d+\.(.+)$/)
  if (match) {
    return `${match[1]}.${match[2]}`
  }

  return decodedFileName
}

// Types
type ConferenceTheme = {
  id: number
  titleEn: string
  icon: string
  color: string
}

type AbstractSubmissionClientProps = {
  conferenceThemes: ConferenceTheme[]
}

// Abstract submission record type definition
type AbstractSubmission = {
  id: string
  title: string
  theme_id: number
  theme?: string
  publication_status: string
  oral_presentation: boolean
  poster_presentation: boolean
  fileName: string
  submissionTime: string
  updateTime?: string
  status: "pending_review" | "approved" | "rejected" | "revision_required"
}

export default function AbstractSubmissionClient({ conferenceThemes }: AbstractSubmissionClientProps) {
  const { user } = useUser()
  const { addToast } = useToast()
  const [isClient, setIsClient] = useState(false)

  // Abstract submission states
  const [abstractSubmissions, setAbstractSubmissions] = useState<AbstractSubmission[]>([])
  const [selectedSubmission, setSelectedSubmission] = useState<AbstractSubmission | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)

  const [isSubmittingAbstract, setIsSubmittingAbstract] = useState(false)

  // Form states
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [hasNewFile, setHasNewFile] = useState(false)
  const [title, setTitle] = useState("")
  const [themeId, setThemeId] = useState("")
  const [publicationStatus, setPublicationStatus] = useState("")
  const [oralPresentation, setOralPresentation] = useState(false)
  const [posterPresentation, setPosterPresentation] = useState(false)

  // Client-side initialization
  useEffect(() => {
    setIsClient(true)
    if (user?.id) {
      fetchAbstractSubmissions()
    }
  }, [user?.id])

  // Get submission permissions based on user status
  const getSubmissionPermissions = () => {
    if (!user) {
      return { canAdd: false, canModify: false, reason: "User not authenticated" }
    }

    if (!user.email_verified) {
      return { canAdd: false, canModify: false, reason: "Please verify your email address first" }
    }

    // Check if user already has a submission
    const hasSubmission = abstractSubmissions.length > 0

    return {
      canAdd: !hasSubmission,
      canModify: hasSubmission,
      reason: hasSubmission ? "You can modify your existing submission" : "You can submit a new abstract",
    }
  }

  // Fetch abstract submissions
  const fetchAbstractSubmissions = async () => {
    if (!user?.id) return

    try {
      const authToken = getAuthToken()
      if (!authToken) {
        addToast({
          type: "error",
          title: "Authentication Error",
          message: "Please login again",
        })
        return
      }

      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/api/abstracts/get/${user.id}`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      })

      const result = (await response.json()) as {
        code?: number
        data?: {
          abstract_id: number
          title: string
          theme_id?: number
          theme: string
          publication_status: string
          oral_presentation: boolean
          poster_presentation: boolean
          abstract_path: string
          submit_time: string
          update_time?: string
        } | null
        message?: string
      }

      if (response.ok && result.code === 200) {
        // API返回单个对象，需要转换为数组并映射字段名
        if (result.data) {
          const mappedData: AbstractSubmission = {
            id: result.data.abstract_id.toString(),
            title: result.data.title,
            theme_id: result.data.theme_id || 1, // 如果没有theme_id，使用默认值
            theme: result.data.theme,
            publication_status: result.data.publication_status,
            oral_presentation: result.data.oral_presentation,
            poster_presentation: result.data.poster_presentation,
            fileName: getOriginalFileName(result.data.abstract_path || ""),
            submissionTime: result.data.submit_time,
            status: "pending_review", // 默认状态，因为API没有返回status字段
            updateTime: result.data.update_time,
          }
          setAbstractSubmissions([mappedData])
        } else {
          setAbstractSubmissions([])
        }
      } else if (result.code === 404) {
        // No submissions found - this is normal
        setAbstractSubmissions([])
      } else {
        console.error("Failed to fetch abstract submissions:", result.message)
        setAbstractSubmissions([])
      }
    } catch (error) {
      console.error("Error fetching abstract submissions:", error)
    }
  }

  // Format date helper
  const formatDate = (dateString: string, options?: { includeTime?: boolean }) => {
    if (!dateString || dateString === "0001-01-01T00:00:00Z") return "Unknown"

    const date = new Date(dateString)
    if (options?.includeTime) {
      return date.toLocaleString()
    }
    return date.toLocaleDateString()
  }

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setUploadedFile(file)
      setHasNewFile(true)

      // Auto-fill title from filename if title is empty
      if (!title.trim()) {
        const fileName = file.name
        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf(".")) || fileName
        setTitle(fileNameWithoutExtension)
      }
    }
  }

  // Clear title
  const clearTitle = () => {
    setTitle("")
  }

  // Reset form
  const resetForm = () => {
    setTitle("")
    setThemeId("")
    setPublicationStatus("")
    setOralPresentation(false)
    setPosterPresentation(false)
    setUploadedFile(null)
    setHasNewFile(false)
  }

  // Load existing submission data into form
  const loadSubmissionIntoForm = (submission: AbstractSubmission) => {
    setTitle(submission.title || "")
    // 如果有theme_id使用它，否则根据theme名称查找ID
    if (submission.theme_id) {
      setThemeId(submission.theme_id.toString())
    } else if (submission.theme) {
      const foundTheme = conferenceThemes.find((t) => t.titleEn === submission.theme)
      setThemeId(foundTheme ? foundTheme.id.toString() : "")
    } else {
      setThemeId("")
    }
    setPublicationStatus(submission.publication_status || "")
    setOralPresentation(submission.oral_presentation || false)
    setPosterPresentation(submission.poster_presentation || false)
    setUploadedFile(null) // File needs to be re-uploaded for updates
    setHasNewFile(false)
  }

  // Handle abstract submission
  const handleSubmitAbstract = async () => {
    // Prevent duplicate submissions
    if (isSubmittingAbstract) {
      return
    }

    // Check submission permissions
    const permissions = getSubmissionPermissions()

    if (!isEditMode && !permissions.canAdd) {
      addToast({
        type: "error",
        title: "Cannot Submit Abstract",
        message: permissions.reason,
      })
      return
    }

    if (isEditMode && !permissions.canModify) {
      addToast({
        type: "error",
        title: "Cannot Modify Abstract",
        message: permissions.reason,
      })
      return
    }

    // Form validation
    if (!title.trim() || !themeId || !publicationStatus) {
      addToast({
        type: "error",
        title: "Validation Error",
        message: "Please fill in all required fields",
      })
      return
    }

    // For new submissions, file is required; for edits, file is optional
    if (!isEditMode && !uploadedFile) {
      addToast({
        type: "error",
        title: "File Required",
        message: "Please upload an abstract file",
      })
      return
    }

    if (!user?.id) {
      addToast({
        type: "error",
        title: "User Error",
        message: "User information not available",
      })
      return
    }

    // Get authentication token
    const authToken = getAuthToken()
    if (!authToken) {
      addToast({
        type: "error",
        title: "Authentication Error",
        message: "Authentication token not found. Please login again.",
      })
      window.location.href = "/login"
      return
    }

    // Set submission state
    setIsSubmittingAbstract(true)

    try {
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL

      // Use FormData to handle file uploads
      const formData = new FormData()

      // Add form fields
      formData.append("title", title.trim())
      formData.append("theme_id", themeId)
      formData.append("publication_status", publicationStatus)
      formData.append("oral_presentation", oralPresentation.toString())
      formData.append("poster_presentation", posterPresentation.toString())

      // Add file (only if new submission or edit mode with new file)
      if (uploadedFile && (!isEditMode || hasNewFile)) {
        formData.append("abstract", uploadedFile)
      }

      // Choose API endpoint and method based on edit mode
      const apiUrl =
        isEditMode && abstractSubmissions.length > 0 && abstractSubmissions[0]
          ? `${apiBaseUrl}/api/abstracts/update/${abstractSubmissions[0].id}`
          : `${apiBaseUrl}/api/abstracts/submit/${user.id}`

      const response = await fetch(apiUrl, {
        method: isEditMode ? "PUT" : "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          // Note: Don't set Content-Type when using FormData, let browser set it automatically
        },
        body: formData,
      })

      const result = (await response.json()) as Record<string, unknown>

      if (response.ok && ((result.code as number) === 0 || (result.code as number) === 200)) {
        addToast({
          type: "success",
          title: "Success",
          message: isEditMode ? "Abstract updated successfully!" : "Abstract submitted successfully!",
        })

        if (isEditMode) {
          // Exit edit mode
          setIsEditMode(false)
          setHasNewFile(false)
        } else {
          // Reset form for new submission
          resetForm()
        }

        // Refresh abstract submissions
        fetchAbstractSubmissions()
      } else {
        addToast({
          type: "error",
          title: "Error",
          message: (result.msg as string) || (result.message as string) || "Failed to submit abstract",
        })
      }
    } catch (_error) {
      addToast({
        type: "error",
        title: "Error",
        message: "An error occurred while submitting abstract",
      })
    } finally {
      // Reset submission state
      setIsSubmittingAbstract(false)
    }
  }

  // If not client-side yet, show loading
  if (!isClient) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Abstract Submission</h1>
          <p className="mt-1 text-gray-600">Loading submission information...</p>
        </div>
        <div className="animate-pulse">
          <div className="h-32 rounded-lg bg-gray-200"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Abstract Template Download */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle>Abstract Template</CardTitle>
            <CardDescription>Download the official abstract template before submission</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between rounded-lg border p-4">
              <div className="flex items-center">
                <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                  <i className="fas fa-file-word text-gray-600"></i>
                </div>
                <div>
                  <h4 className="font-medium">IFMB 2025 Abstract Template</h4>
                  <p className="text-sm text-gray-600">Official template with formatting guidelines</p>
                </div>
              </div>
              <Button
                variant="outline"
                onClick={() => {
                  const templateUrl = getDocumentUrl("Abstract-template.docx")
                  window.open(templateUrl, "_blank")
                }}
              >
                <i className="fas fa-download mr-2"></i>
                Download Template
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Submitted Abstract Information */}
        {abstractSubmissions.length > 0 && !isEditMode && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <i className="fas fa-file-check mr-2 text-green-600"></i>
                  <span>Your Abstract Submission</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (abstractSubmissions[0]) {
                        setSelectedSubmission(abstractSubmissions[0])
                        setIsDetailDialogOpen(true)
                      }
                    }}
                  >
                    <i className="fas fa-eye mr-2"></i>
                    View Details
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (abstractSubmissions[0]) {
                        loadSubmissionIntoForm(abstractSubmissions[0])
                        setIsEditMode(true)
                      }
                    }}
                    disabled={!user?.email_verified}
                  >
                    <i className="fas fa-edit mr-2"></i>
                    Edit Submission
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>
                Your abstract has been submitted successfully. You can view details or make modifications.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {abstractSubmissions.map((submission, index) => (
                <div key={submission.id || `submission-${index}`} className="space-y-6">
                  {/* Basic Info */}
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                      <Label className="text-sm font-medium text-gray-600">Submission Date</Label>
                      <p className="mt-2 text-sm font-medium">
                        {formatDate(submission.submissionTime, { includeTime: true })}
                      </p>
                    </div>
                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                      <Label className="text-sm font-medium text-gray-600">Last Updated</Label>
                      <p className="mt-2 text-sm font-medium">
                        {submission.updateTime && submission.updateTime !== "0001-01-01T00:00:00Z"
                          ? formatDate(submission.updateTime, { includeTime: true })
                          : "Not updated"}
                      </p>
                    </div>
                  </div>

                  {/* Abstract Details */}
                  <div className="rounded-lg border border-gray-200 bg-white p-6">
                    <div className="mb-4 flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900">Abstract Information</h3>
                    </div>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Abstract Title</Label>
                        <p className="mt-1 text-sm font-medium text-gray-900">{submission.title || "Unknown Title"}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Conference Theme</Label>
                        <p className="mt-1 text-sm text-gray-800">
                          {submission.theme ||
                            conferenceThemes.find((t) => t.id === submission.theme_id)?.titleEn ||
                            "Unknown"}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Publication Status</Label>
                        <p className="mt-1 text-sm text-gray-800 capitalize">
                          {submission.publication_status ? submission.publication_status.replace("_", " ") : "Unknown"}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Submitted File</Label>
                        <p className="mt-1 text-sm text-gray-800">{submission.fileName || "Unknown File"}</p>
                      </div>
                    </div>

                    {/* Presentation Types */}
                    <div className="mt-4">
                      <Label className="text-sm font-medium text-gray-700">Presentation Types</Label>
                      <div className="mt-2 flex gap-2">
                        {submission.oral_presentation && (
                          <Badge key="oral" variant="outline" className="border-blue-300 bg-blue-100 text-blue-800">
                            <i className="fas fa-microphone mr-1"></i>
                            Oral Presentation
                          </Badge>
                        )}
                        {submission.poster_presentation && (
                          <Badge
                            key="poster"
                            variant="outline"
                            className="border-purple-300 bg-purple-100 text-purple-800"
                          >
                            <i className="fas fa-image mr-1"></i>
                            Poster Presentation
                          </Badge>
                        )}
                        {!submission.oral_presentation && !submission.poster_presentation && (
                          <span key="none" className="text-sm text-gray-600">
                            No presentation types selected
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Abstract Submission Form - 只在没有提交或编辑模式时显示 */}
        {(abstractSubmissions.length === 0 || isEditMode) && (
          <Card>
            <CardHeader>
              <CardTitle>
                {abstractSubmissions.length > 0 && isEditMode ? "Edit Abstract Submission" : "Abstract Submission"}
              </CardTitle>
              <CardDescription>
                {abstractSubmissions.length > 0 && isEditMode
                  ? "Update your existing abstract submission"
                  : "Submit your research abstract for IFMB 2025"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {!user?.email_verified ? (
                <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
                  <div className="flex items-center">
                    <i className="fas fa-exclamation-triangle mr-3 text-amber-600"></i>
                    <div>
                      <h4 className="font-medium text-amber-900">Email Verification Required</h4>
                      <p className="text-sm text-amber-700">
                        Please verify your email address before submitting abstracts. Check your inbox for the
                        verification email.
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* File Upload */}
                  <div className="space-y-2">
                    <Label htmlFor="abstractFile">
                      Abstract File {!isEditMode && <span className="text-red-500">*</span>}
                    </Label>
                    <div className="rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
                      <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 text-gray-400">
                        <i className="fas fa-cloud-upload-alt text-xl"></i>
                      </div>
                      <div className="mb-4">
                        <p className="mb-2 text-sm text-gray-600">
                          {uploadedFile
                            ? uploadedFile.name
                            : isEditMode && abstractSubmissions[0]
                              ? `Current file: ${abstractSubmissions[0].fileName}`
                              : "Upload your abstract file"}
                        </p>
                        <p className="text-xs text-gray-500">
                          Supported formats: .doc, .docx, .pdf (Max size: 10MB)
                          {isEditMode && " - Upload new file to replace current one"}
                        </p>
                      </div>
                      <input
                        type="file"
                        id="abstractFile"
                        accept=".doc,.docx,.pdf"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                      <Button variant="outline" onClick={() => document.getElementById("abstractFile")?.click()}>
                        <i className="fas fa-file-upload mr-2"></i>
                        {isEditMode ? "Replace File" : "Choose File"}
                      </Button>
                    </div>
                  </div>

                  {/* Title */}
                  <div className="space-y-2">
                    <Label htmlFor="title">
                      Abstract Title <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="title"
                        placeholder="Enter the title of your abstract"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        className="pr-8"
                      />
                      {title && (
                        <button
                          type="button"
                          onClick={clearTitle}
                          className="absolute top-1/2 right-2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          <i className="fas fa-times text-sm"></i>
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Conference Theme */}
                  <div className="space-y-2">
                    <Label htmlFor="theme">
                      Conference Theme <span className="text-red-500">*</span>
                    </Label>
                    <Select value={themeId} onValueChange={setThemeId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select conference theme" />
                      </SelectTrigger>
                      <SelectContent>
                        {conferenceThemes.map((theme) => (
                          <SelectItem key={theme.id} value={theme.id.toString()}>
                            {theme.titleEn}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Publication Status */}
                  <div className="space-y-2">
                    <Label htmlFor="publicationStatus">
                      Publication Status <span className="text-red-500">*</span>
                    </Label>
                    <Select value={publicationStatus} onValueChange={setPublicationStatus}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select publication status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="unpublished">Unpublished</SelectItem>
                        <SelectItem value="partially">Partially Published</SelectItem>
                        <SelectItem value="published">Published</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Presentation Types */}
                  <div className="space-y-4">
                    <Label>
                      Presentation Type <span className="text-red-500">*</span>
                    </Label>
                    <p className="text-sm text-gray-600">Select your preferred presentation types</p>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <Label htmlFor="oralPresentation" className="font-medium">
                            Oral Presentation
                          </Label>
                          <p className="text-sm text-gray-600">Present your research in a talk session</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-muted-foreground text-sm">{oralPresentation ? "Yes" : "No"}</span>
                          <Switch checked={oralPresentation} onCheckedChange={setOralPresentation} />
                        </div>
                      </div>
                      <div className="flex items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <Label htmlFor="posterPresentation" className="font-medium">
                            Poster Presentation
                          </Label>
                          <p className="text-sm text-gray-600">Display your research as a poster</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-muted-foreground text-sm">{posterPresentation ? "Yes" : "No"}</span>
                          <Switch checked={posterPresentation} onCheckedChange={setPosterPresentation} />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end space-x-3 pt-4">
                    {isEditMode && (
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsEditMode(false)
                          resetForm()
                        }}
                      >
                        <i className="fas fa-times mr-2"></i>
                        Cancel
                      </Button>
                    )}
                    <Button
                      onClick={handleSubmitAbstract}
                      disabled={
                        isSubmittingAbstract ||
                        !title.trim() ||
                        !themeId ||
                        !publicationStatus ||
                        (!isEditMode && !uploadedFile)
                      }
                    >
                      {isSubmittingAbstract ? (
                        <>
                          <i className="fas fa-spinner fa-spin mr-2"></i>
                          {isEditMode ? "Updating..." : "Submitting..."}
                        </>
                      ) : (
                        <>
                          <i className={`fas ${isEditMode ? "fa-save" : "fa-paper-plane"} mr-2`}></i>
                          {isEditMode ? "Update Abstract" : "Submit Abstract"}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Submission Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Abstract Submission Details</DialogTitle>
            <DialogDescription>Detailed information about your abstract submission</DialogDescription>
          </DialogHeader>
          {selectedSubmission && (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Abstract Title</Label>
                  <p className="mt-1 text-sm font-medium">{selectedSubmission.title || "Unknown Title"}</p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Conference Theme</Label>
                  <p className="mt-1 text-sm">
                    {conferenceThemes.find((t) => t.id === selectedSubmission.theme_id)?.titleEn || "Unknown"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Publication Status</Label>
                  <p className="mt-1 text-sm capitalize">
                    {selectedSubmission.publication_status
                      ? selectedSubmission.publication_status.replace("_", " ")
                      : "Unknown"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Submission Date</Label>
                  <p className="mt-1 text-sm">{formatDate(selectedSubmission.submissionTime, { includeTime: true })}</p>
                </div>
                {selectedSubmission.updateTime && (
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Last Updated</Label>
                    <p className="mt-1 text-sm">{formatDate(selectedSubmission.updateTime, { includeTime: true })}</p>
                  </div>
                )}
              </div>

              {/* Presentation Types */}
              <div>
                <Label className="text-sm font-medium text-gray-600">Presentation Types</Label>
                <div className="mt-2 flex gap-2">
                  {selectedSubmission.oral_presentation && (
                    <Badge variant="outline" className="bg-blue-50 text-blue-700">
                      <i className="fas fa-microphone mr-1"></i>
                      Oral Presentation
                    </Badge>
                  )}
                  {selectedSubmission.poster_presentation && (
                    <Badge variant="outline" className="bg-purple-50 text-purple-700">
                      <i className="fas fa-image mr-1"></i>
                      Poster Presentation
                    </Badge>
                  )}
                  {!selectedSubmission.oral_presentation && !selectedSubmission.poster_presentation && (
                    <span className="text-sm text-gray-500">No presentation types selected</span>
                  )}
                </div>
              </div>

              {/* File Information */}
              <div>
                <Label className="text-sm font-medium text-gray-600">Submitted File</Label>
                <div className="mt-2 flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 p-3">
                  <i className="fas fa-file-alt text-gray-400"></i>
                  <span className="text-sm font-medium">{selectedSubmission.fileName || "Unknown File"}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4">
                <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>
                  Close
                </Button>
                {user?.email_verified && (
                  <Button
                    onClick={() => {
                      loadSubmissionIntoForm(selectedSubmission)
                      setIsEditMode(true)
                      setIsDetailDialogOpen(false)
                    }}
                  >
                    <i className="fas fa-edit mr-2"></i>
                    Edit Submission
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
