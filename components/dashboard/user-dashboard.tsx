"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { useEffect, useState } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { getUserProfile, UserProfileData } from "@/utils/users-api"

// 用户类型定义
type User = {
  id: string
  name: string
  email: string
  role: "user" | "admin"
  emailVerified?: boolean
  membership?: string
  organization?: string
  country?: string
}

export default function UserDashboard() {
  const [user, setUser] = useState<User | null>(null)
  const [error, setError] = useState<string>("")

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        // 检查用户登录状态
        const userData = localStorage.getItem("user")
        if (!userData) {
          window.location.href = "/login"
          return
        }

        let parsedData
        try {
          parsedData = JSON.parse(userData) as Record<string, unknown>
        } catch (jsonError) {
          console.error("Invalid JSON in localStorage:", jsonError)
          window.location.href = "/login"
          return
        }

        // 提取基本用户信息和认证令牌
        const parsedDataTyped = parsedData as Record<string, unknown>
        const basicUserInfo =
          parsedDataTyped.user_info || (parsedDataTyped.data as Record<string, unknown>)?.user_info || parsedDataTyped
        const authToken =
          (parsedDataTyped.token as string) || ((parsedDataTyped.data as Record<string, unknown>)?.token as string)

        if (!basicUserInfo || !authToken) {
          console.error("User info or token not found in localStorage")
          window.location.href = "/login"
          return
        }

        // 从 API 获取完整的用户数据
        const basicUserInfoTyped = basicUserInfo as Record<string, unknown>
        const userProfileData: UserProfileData = await getUserProfile(
          (basicUserInfoTyped.id as string)?.toString() || "",
          authToken
        )

        // 构建标准化的用户对象，使用 API 数据
        const roleValue = userProfileData.role || "user"
        const standardUser: User = {
          id: userProfileData.id.toString(),
          name: userProfileData.name,
          email: userProfileData.email,
          role: roleValue === "admin" || roleValue === "user" ? roleValue : "user",
          emailVerified: userProfileData.email_verified === true,
          membership: userProfileData.membership,
          organization: userProfileData.organization,
          country: userProfileData.country,
        }

        setUser(standardUser)
      } catch (error) {
        console.error("Error fetching user data:", error)
        // 如果 API 调用失败，可以考虑回退到 localStorage 数据
        setError("Failed to load user data. Please try refreshing the page.")
      }
    }

    fetchUserData()
  }, [])

  if (!user) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          {error ? (
            <>
              <h1 className="mb-4 text-2xl font-bold text-red-600">Error</h1>
              <p className="mb-4 text-gray-600">{error}</p>
              <Button onClick={() => window.location.reload()}>Refresh Page</Button>
            </>
          ) : (
            <>
              <h1 className="mb-4 text-2xl font-bold text-gray-900">Please Login</h1>
              <p className="mb-4 text-gray-600">You need to login to access your dashboard.</p>
              <Link href="/login">
                <Button>Login</Button>
              </Link>
            </>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="mb-2 text-3xl font-bold text-gray-900">Welcome back, {user.name}!</h1>
          <p className="text-gray-600">Manage your conference registration and profile</p>
        </motion.div>

        {/* Email Verification Notice */}
        {user.role !== "admin" && !user.emailVerified && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8"
          >
            <Card className="border-border bg-muted">
              <CardContent className="pt-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <i className="fas fa-exclamation-triangle text-muted-foreground text-xl"></i>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-foreground mb-2 text-lg font-medium">Email Verification Required</h3>
                    <p className="text-muted-foreground mb-4">
                      Your email address has not been verified yet. To access all conference features and pages, please
                      verify your email address. You can only access the Dashboard and Profile pages until verification
                      is complete.
                    </p>
                    <div className="flex flex-col gap-3 sm:flex-row">
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Resend verification email functionality
                          // This will be implemented with proper toast notification
                        }}
                      >
                        <i className="fas fa-envelope mr-2"></i>
                        Resend Verification Email
                      </Button>
                      <Link href="/profile">
                        <Button variant="outline">
                          <i className="fas fa-user mr-2"></i>
                          Go to Profile
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-3"
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Payment Status</CardTitle>
              <i className="fas fa-credit-card text-green-600"></i>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Pending</div>
              <p className="text-muted-foreground text-xs">Registration fee</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Submission Status</CardTitle>
              <i className="fas fa-file-alt text-blue-600"></i>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Not Started</div>
              <p className="text-muted-foreground text-xs">Abstract submission</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Days Until Event</CardTitle>
              <i className="fas fa-calendar text-purple-600"></i>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">45</div>
              <p className="text-muted-foreground text-xs">IFMB 2025</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="submission">Submission</TabsTrigger>
              <TabsTrigger value="accommodation">Accommodation</TabsTrigger>
            </TabsList>

            {/* Profile Tab */}
            <TabsContent value="profile" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name</label>
                        <p className="mt-1 text-sm text-gray-900">{user.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Email</label>
                        <p className="mt-1 text-sm text-gray-900">{user.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Role</label>
                        <p className="mt-1 text-sm text-gray-900 capitalize">{user.role}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Email Verification</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {user.emailVerified ? (
                            <span className="flex items-center text-green-600">
                              <i className="fas fa-check-circle mr-1"></i> Verified
                            </span>
                          ) : (
                            <span className="flex items-center text-amber-600">
                              <i className="fas fa-exclamation-circle mr-1"></i> Not Verified
                            </span>
                          )}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Membership</label>
                        <p className="mt-1 text-sm text-gray-900">{user.membership || "Not specified"}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Organization</label>
                        <p className="mt-1 text-sm text-gray-900">{user.organization || "Not specified"}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Country</label>
                        <p className="mt-1 text-sm text-gray-900">{user.country || "Not specified"}</p>
                      </div>
                    </div>
                    <div className="pt-4">
                      <Link href="/profile">
                        <Button>
                          <i className="fas fa-edit mr-2"></i>
                          Edit Profile
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Submission Tab */}
            <TabsContent value="submission" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Abstract Submission</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 pt-0.5">
                          <i className="fas fa-info-circle text-blue-500"></i>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-blue-800">Submission Guidelines</h3>
                          <div className="mt-2 text-sm text-blue-700">
                            <p>Abstract submissions are now open for IFMB 2025. Please follow these guidelines:</p>
                            <ul className="mt-1 list-disc space-y-1 pl-5">
                              <li>Maximum 700 words</li>
                              <li>Include title, authors, and affiliations</li>
                              <li>Submission deadline: July 15, 2025</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="rounded-lg border border-gray-200 p-4">
                      <h3 className="mb-4 text-lg font-medium">Submission Status</h3>
                      <div className="mb-6 flex items-center space-x-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                          <i className="fas fa-file-alt text-gray-500"></i>
                        </div>
                        <div className="h-0.5 flex-1 bg-gray-200"></div>
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                          <i className="fas fa-check text-gray-500"></i>
                        </div>
                        <div className="h-0.5 flex-1 bg-gray-200"></div>
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                          <i className="fas fa-envelope text-gray-500"></i>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 text-center text-sm">
                        <div>Prepare</div>
                        <div>Submit</div>
                        <div>Notification</div>
                      </div>
                    </div>

                    <div className="flex justify-center">
                      <Link href="/dashboard/submission">
                        <Button>
                          <i className="fas fa-paper-plane mr-2"></i>
                          Submit Now
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Accommodation Tab */}
            <TabsContent value="accommodation" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Accommodation Options</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 pt-0.5">
                          <i className="fas fa-hotel text-amber-500"></i>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-amber-800">Accommodation Information</h3>
                          <div className="mt-2 text-sm text-amber-700">
                            <p>
                              We have negotiated special rates with several hotels near the conference venue. Booking
                              through our platform gives you access to these discounted rates.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div className="overflow-hidden rounded-lg border">
                        <div className="flex h-40 items-center justify-center bg-gray-200">
                          <i className="fas fa-hotel text-4xl text-gray-400"></i>
                        </div>
                        <div className="p-4">
                          <h3 className="mb-1 font-medium">Conference Hotel</h3>
                          <p className="mb-2 text-sm text-gray-500">★★★★☆ | 0.2 km from venue</p>
                          <p className="mb-4 text-sm">Special rate: $120/night</p>
                          <Button variant="outline" size="sm" className="w-full">
                            <i className="fas fa-info-circle mr-2"></i>
                            View Details
                          </Button>
                        </div>
                      </div>

                      <div className="overflow-hidden rounded-lg border">
                        <div className="flex h-40 items-center justify-center bg-gray-200">
                          <i className="fas fa-hotel text-4xl text-gray-400"></i>
                        </div>
                        <div className="p-4">
                          <h3 className="mb-1 font-medium">Budget Option</h3>
                          <p className="mb-2 text-sm text-gray-500">★★★☆☆ | 1.5 km from venue</p>
                          <p className="mb-4 text-sm">Special rate: $75/night</p>
                          <Button variant="outline" size="sm" className="w-full">
                            <i className="fas fa-info-circle mr-2"></i>
                            View Details
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-center">
                      <Link href="/dashboard/accommodation">
                        <Button>
                          <i className="fas fa-bed mr-2"></i>
                          Book Accommodation
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* 通知公告 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mb-8"
        >
          <Card className="border-border bg-card">
            <CardHeader>
              <CardTitle className="text-foreground">
                <i className="fas fa-bullhorn mr-2"></i>
                Announcements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-border border-l-4 py-2 pl-4">
                  <h3 className="text-foreground font-medium">IFMB 2025 Registration Open</h3>
                  <p className="text-muted-foreground mt-1 text-sm">
                    The International Forum on Molecular Biology (IFMB) 2025 registration is now open. Please complete
                    early bird registration by March 15, 2025, to enjoy discounted rates.
                  </p>
                  <p className="text-muted-foreground mt-1 text-xs">2025-02-01</p>
                </div>

                <div className="border-border border-l-4 py-2 pl-4">
                  <h3 className="text-foreground font-medium">Abstract Submission Deadline Extended</h3>
                  <p className="text-muted-foreground mt-1 text-sm">
                    Due to numerous requests from researchers, the abstract submission deadline has been extended to
                    July 15, 2025.
                  </p>
                  <p className="text-muted-foreground mt-1 text-xs">2025-05-20</p>
                </div>

                <div className="border-border border-l-4 py-2 pl-4">
                  <h3 className="text-foreground font-medium">New Keynote Speaker Added</h3>
                  <p className="text-muted-foreground mt-1 text-sm">
                    We are pleased to announce that Nobel laureate Dr. Maria Chen will deliver a keynote speech at the
                    conference.
                  </p>
                  <p className="text-muted-foreground mt-1 text-xs">2025-06-01</p>
                </div>

                <div className="text-right">
                  <Button variant="outline">
                    <i className="fas fa-list-ul mr-2"></i>
                    View All Announcements
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
