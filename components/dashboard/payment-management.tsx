"use client"

import { motion } from "framer-motion"
import { useEffect, useState } from "react"
import { useUser } from "@/components/layout/user-context"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/toast"
import { getAuthToken } from "@/utils/auth"

// Payment record type definition
type PaymentRecord = {
  tid: number
  request_user: number
  team_users: string
  review_id: number
  region: string
  transaction_id: string
  amount: number
  evidence: string
  notes: string
  transaction_time: string
  t_create_time: string
  rid: number
  reviewer: number
  approved: boolean
  review_time: string
  method_name: string
  request_username?: string
  review_user?: string
  review_username?: string
}

// Invoice information type definition
type InvoiceInfo = {
  iid: number
  tid: number
  tax_id: string
  organization: string
  address: string
  contact_name: string
  contact_email: string
  contact_phone: string
  instruction: string
  create_time: string
}

// Registration fee data from /registration page
const registrationFees = [
  {
    type: "Student",
    earlyBird: { price: 1400, currency: "CNY", deadline: "August 15, 2025" },
    regular: { price: 1600, currency: "CNY", deadline: "September 15, 2025" },
  },
  {
    type: "Non-student (postdocs, sponsors, PIs and teachers)",
    earlyBird: { price: 2200, currency: "CNY", deadline: "August 15, 2025" },
    regular: { price: 2400, currency: "CNY", deadline: "September 15, 2025" },
  },
]

// Payment information from /registration page
const paymentInfo = {
  international: {
    beneficiary: "Huazhong Agricultural University",
    bank: "Bank of China Hubei Branch",
    accountNumber: "************",
    address: "Bank of China Hubei Branch, Donghu Subbranch 430079",
    swiftCode: "BKCHCNBJ600",
  },
  domestic: {
    beneficiary: "Huazhong Agricultural University",
    bank: "Bank of China Hubei Branch, Huanong Subbranch",
    beneficiaryNumber: "************",
    account: "************",
    address: "1 Shizishan St, Hongshan District, Wuhan 430070, China",
  },
}

export default function PaymentManagement() {
  const { user } = useUser()
  const { addToast } = useToast()
  const [paymentMethod, setPaymentMethod] = useState<string>("international")
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [isClient, setIsClient] = useState(false)

  // 安全的日期格式化函数
  const formatDate = (dateString: string, options: { includeTime?: boolean } = {}) => {
    if (!isClient || !dateString) return ""
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return ""
      return options.includeTime ? date.toLocaleString() : date.toLocaleDateString()
    } catch (_error) {
      return ""
    }
  }

  // 新增的表单状态
  const [region, setRegion] = useState<string>("domestic")
  const [teamMembers, setTeamMembers] = useState<string>("")
  const [teamMemberTags, setTeamMemberTags] = useState<string[]>([])
  const [method, setMethod] = useState<number>(1) // 1: bank transfer, 2: AliPay
  const [transactionId, setTransactionId] = useState<string>("")
  const [transactionTime, setTransactionTime] = useState<string>("")
  const [amount, setAmount] = useState<string>("")
  const [notes, setNotes] = useState<string>("")

  // 表单验证状态
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [isFormValid, setIsFormValid] = useState(false)

  // 支付记录相关状态
  const [paymentRecords, setPaymentRecords] = useState<PaymentRecord[]>([])
  const [selectedRecord, setSelectedRecord] = useState<PaymentRecord | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [hasNewFile, setHasNewFile] = useState(false) // 跟踪是否有新文件上传
  const [isSubmittingPayment, setIsSubmittingPayment] = useState(false) // 防止重复提交支付信息

  // 发票信息状态
  const [invoiceData, setInvoiceData] = useState({
    taxId: "",
    organization: "",
    address: "",
    contactName: "",
    contactEmail: "",
    contactPhone: "",
    instruction: "",
  })
  const [existingInvoice, setExistingInvoice] = useState<InvoiceInfo | null>(null)
  const [isInvoiceLoading, setIsInvoiceLoading] = useState(false)
  const [showInvoiceForm, setShowInvoiceForm] = useState(false)
  const [isSubmittingInvoice, setIsSubmittingInvoice] = useState(false) // 防止重复提交发票信息

  // 使用useState来避免hydration不匹配
  const [isEarlyBird, setIsEarlyBird] = useState(true) // 默认为true避免hydration问题

  // 确保只在客户端执行日期相关逻辑
  useEffect(() => {
    setIsClient(true)
    if (user?.id) {
      fetchPaymentRecords()
      fetchInvoiceInfo()
    }
    // 在客户端初始化时设置用户相关的发票数据
    if (user) {
      setInvoiceData((prev) => ({
        ...prev,
        organization: user.organization || prev.organization,
        contactName: user.name || prev.contactName,
        contactEmail: user.email || prev.contactEmail,
        contactPhone: user.phone || prev.contactPhone,
      }))
    }
  }, [user?.id])

  // 当用户信息变化时更新发票数据
  useEffect(() => {
    if (user) {
      setInvoiceData((prev) => ({
        ...prev,
        organization: user.organization || prev.organization,
        contactName: user.name || prev.contactName,
        contactEmail: user.email || prev.contactEmail,
        contactPhone: user.phone || prev.contactPhone,
      }))
    }
  }, [user])

  // 在客户端计算实际的早鸟状态
  useEffect(() => {
    if (isClient) {
      const now = new Date()
      const earlyDeadline = new Date("2025-08-15")
      setIsEarlyBird(now <= earlyDeadline)
    }
  }, [isClient])

  // 获取用户支付记录
  const fetchPaymentRecords = async () => {
    if (!user?.id) return

    try {
      const authToken = getAuthToken()
      if (!authToken) {
        return
      }

      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/api/payment/${user.id}`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      })

      const result = (await response.json()) as Record<string, unknown>

      if (response.ok && ((result.code as number) === 0 || (result.code as number) === 200)) {
        // API返回单个对象，我们需要将其包装为数组
        const paymentData = result.data as PaymentRecord
        if (paymentData && paymentData.tid) {
          setPaymentRecords([paymentData])
        } else {
          setPaymentRecords([])
        }
      } else {
      }
    } catch (_error) {}
  }

  // 获取发票信息
  const fetchInvoiceInfo = async () => {
    if (!user?.id) return

    setIsInvoiceLoading(true)
    try {
      const authToken = getAuthToken()
      if (!authToken) {
        return
      }

      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/api/payment/invoice/${user.id}`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      })

      const result = (await response.json()) as Record<string, unknown>

      if (response.ok && ((result.code as number) === 0 || (result.code as number) === 200)) {
        const invoiceInfo = result.data as InvoiceInfo
        if (invoiceInfo) {
          setExistingInvoice(invoiceInfo)
          // 如果有现有发票信息，更新表单数据
          setInvoiceData({
            taxId: invoiceInfo.tax_id || "",
            organization: invoiceInfo.organization || "",
            address: invoiceInfo.address || "",
            contactName: invoiceInfo.contact_name || "",
            contactEmail: invoiceInfo.contact_email || "",
            contactPhone: invoiceInfo.contact_phone || "",
            instruction: invoiceInfo.instruction || "",
          })
        } else {
          setExistingInvoice(null)
          // 如果没有现有发票信息，默认显示表单
          setShowInvoiceForm(true)
        }
      } else {
        // 如果没有找到发票信息，这是正常的，不需要显示错误
        // 忽略404错误（表示没有发票信息）
        setExistingInvoice(null)
        // 如果没有现有发票信息，默认显示表单
        setShowInvoiceForm(true)
      }
    } catch (_error) {
      setExistingInvoice(null)
      // 如果出错，默认显示表单
      setShowInvoiceForm(true)
    } finally {
      setIsInvoiceLoading(false)
    }
  }

  // 处理编辑发票信息
  const handleEditInvoice = () => {
    setShowInvoiceForm(true)
  }

  // 取消编辑发票信息
  const handleCancelInvoiceEdit = () => {
    if (existingInvoice) {
      // 如果有现有发票信息，恢复原始数据
      setInvoiceData({
        taxId: existingInvoice.tax_id || "",
        organization: existingInvoice.organization || "",
        address: existingInvoice.address || "",
        contactName: existingInvoice.contact_name || "",
        contactEmail: existingInvoice.contact_email || "",
        contactPhone: existingInvoice.contact_phone || "",
        instruction: existingInvoice.instruction || "",
      })
      setShowInvoiceForm(false)
    }
  }

  // 实时表单验证函数
  const validateField = (fieldName: string, value: string): string => {
    const stringValue = String(value)

    switch (fieldName) {
      case "transactionId":
        if (!stringValue || stringValue.trim() === "") return "Please enter transaction ID"
        if (stringValue.trim().length < 3) return "Transaction ID must be at least 3 characters"
        return ""

      case "amount":
        if (!stringValue || stringValue.trim() === "") return "Please enter payment amount"
        const amountNum = parseFloat(stringValue)
        if (isNaN(amountNum) || amountNum <= 0) return "Payment amount must be a positive number"
        if (amountNum > 999999) return "Payment amount cannot exceed 999,999"
        return ""

      case "transactionTime":
        if (!stringValue) return "Please select transaction time"
        const transactionDate = new Date(stringValue)
        const now = new Date()
        const oneYearAgo = new Date()
        oneYearAgo.setFullYear(now.getFullYear() - 1)
        if (transactionDate > now) return "Transaction time cannot be in the future"
        if (transactionDate < oneYearAgo) return "Transaction time cannot be more than one year ago"
        return ""

      case "teamMembers":
        // 暂时移除用户名验证
        return ""

      default:
        return ""
    }
  }

  // 更新字段错误状态
  const updateFieldError = (fieldName: string, value: string) => {
    const error = validateField(fieldName, value)
    setFieldErrors(prev => ({
      ...prev,
      [fieldName]: error
    }))
  }

  // 处理团队成员输入
  const handleTeamMembersChange = (value: string) => {
    setTeamMembers(value)
    updateFieldError("teamMembers", value)

    // 当用户输入逗号或按回车时，创建标签
    if (value.includes(",") || value.includes("\n")) {
      const newMembers = value
        .split(/[,\n]/)
        .map((member) => member.trim())
        .filter((member) => member !== "")
      const lastMember = newMembers.pop() || "" // 获取最后一个可能未完成的输入

      // 添加新的标签，去除重复
      const allTags = [...teamMemberTags, ...newMembers]
      const updatedTags = Array.from(new Set(allTags))
      setTeamMemberTags(updatedTags)
      setTeamMembers(lastMember) // 保留最后一个未完成的输入
    }
  }

  // 删除团队成员标签
  const removeTeamMember = (memberToRemove: string) => {
    setTeamMemberTags(teamMemberTags.filter((member) => member !== memberToRemove))
  }

  // 添加团队成员标签（当失去焦点时）
  const handleTeamMembersBlur = () => {
    if (teamMembers.trim() !== "") {
      const newMember = teamMembers.trim()
      if (!teamMemberTags.includes(newMember)) {
        setTeamMemberTags([...teamMemberTags, newMember])
      }
      setTeamMembers("")
    }
  }

  // 进入编辑模式
  const handleEditPayment = (record: PaymentRecord) => {
    setRegion(record.region)
    // 根据method_name设置method值
    setMethod(record.method_name === "bank transfer" ? 1 : 2)
    setTransactionId(record.transaction_id)
    // 处理时间格式转换
    const timeStr = record.transaction_time.replace("+08:00", "").replace("Z", "")
    setTransactionTime(timeStr)
    setAmount(record.amount.toString())

    // 处理团队成员数据
    const teamUsers = record.team_users || ""
    if (teamUsers) {
      const members = teamUsers
        .split(",")
        .map((member) => member.trim())
        .filter((member) => member !== "")
      setTeamMemberTags(members)
      setTeamMembers("")
    } else {
      setTeamMemberTags([])
      setTeamMembers("")
    }

    setNotes(record.notes || "")
    setIsEditMode(true)
    setHasNewFile(false) // 进入编辑模式时重置新文件标志
    setUploadedFile(null) // 清空文件选择
  }

  // 取消编辑模式
  const handleCancelEdit = () => {
    setIsEditMode(false)
    setShowAddForm(false)
    // 重置表单
    setRegion("domestic")
    setMethod(1)
    setTransactionId("")
    setTransactionTime("")
    setAmount("")
    setTeamMembers("")
    setNotes("")
    setUploadedFile(null)
    setHasNewFile(false) // 重置新文件标志
  }

  // 查看支付凭证
  const handleViewReceipt = (evidenceUrl: string) => {
    if (!evidenceUrl) {
      addToast({
        type: "error",
        title: "Error",
        message: "Receipt URL not available",
      })
      return
    }

    try {
      // 在新标签页中打开凭证
      window.open(evidenceUrl, "_blank", "noopener,noreferrer")
    } catch (error) {
      console.error("Error opening receipt:", error)
      addToast({
        type: "error",
        title: "Error",
        message: "Failed to open receipt",
      })
    }
  }

  // 获取文件类型图标
  const getFileIcon = (evidenceUrl: string) => {
    if (!evidenceUrl) return "fas fa-file"

    const url = evidenceUrl.toLowerCase()
    if (url.includes(".pdf")) return "fas fa-file-pdf"
    if (url.includes(".jpg") || url.includes(".jpeg") || url.includes(".png") || url.includes(".gif"))
      return "fas fa-file-image"
    return "fas fa-file"
  }

  // 检查支付记录是否已被审核
  const isPaymentReviewed = (record: PaymentRecord) => {
    return record.reviewer > 0 || (record.review_time && record.review_time !== "0001-01-01T00:00:00Z")
  }

  // 检查支付记录是否已批准
  const isPaymentApproved = (record: PaymentRecord) => {
    return record.approved === true || (record.approved as unknown) === "true"
  }

  // 检查支付操作权限
  const getPaymentPermissions = () => {
    const hasPaymentRecord = paymentRecords.length > 0
    const latestRecord = hasPaymentRecord ? paymentRecords[0] : null

    if (!hasPaymentRecord) {
      // 没有支付记录时允许添加
      return {
        canAdd: true,
        canModify: false,
        reason: "",
      }
    }

    // 使用辅助函数检查状态
    const isApproved = latestRecord ? isPaymentApproved(latestRecord) : false
    const isReviewed = latestRecord ? isPaymentReviewed(latestRecord) : false
    const isRejected = isReviewed && !isApproved

    if (isApproved) {
      // 已批准时不允许添加和修改
      return {
        canAdd: false,
        canModify: false,
        reason: "Payment has been approved and cannot be modified",
      }
    }

    if (isRejected) {
      // 被拒绝时允许添加新的支付记录
      return {
        canAdd: true,
        canModify: false,
        reason: "Previous payment was rejected, you can submit a new payment",
      }
    }

    // 有支付记录但未被审核时，不允许添加，允许修改
    return {
      canAdd: false,
      canModify: true,
      reason: "Payment is pending review, you can only modify existing payment information",
    }
  }

  // 提交或更新发票信息
  const handleSubmitInvoice = async () => {
    // 防止重复提交
    if (isSubmittingInvoice) {
      return
    }

    // 检查是否有已批准的支付记录
    const approvedPayment = paymentRecords.find((record) => isPaymentApproved(record))
    if (!approvedPayment) {
      addToast({
        type: "error",
        title: "Error",
        message: "You must have an approved payment before submitting invoice information",
      })
      return
    }

    // 表单验证
    const { taxId, organization, address, contactName, contactEmail, contactPhone, instruction } = invoiceData
    if (!taxId || !organization || !address || !contactName || !contactEmail || !contactPhone || !instruction) {
      addToast({
        type: "error",
        title: "Error",
        message: "Please fill in all required fields",
      })
      return
    }

    // 获取认证token
    const authToken = getAuthToken()
    if (!authToken) {
      addToast({
        type: "error",
        title: "Error",
        message: "Authentication token not found, please login again",
      })
      return
    }

    // 设置提交状态
    setIsSubmittingInvoice(true)

    try {
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL

      // 判断是新建还是更新
      const isUpdate = existingInvoice !== null
      const apiUrl = isUpdate
        ? `${apiBaseUrl}/api/payment/invoice/${existingInvoice.iid}`
        : `${apiBaseUrl}/api/payment/invoice/${user?.id}`
      const method = isUpdate ? "PUT" : "POST"

      // 准备表单数据
      const formData = new FormData()

      // 对于新建，需要tid；对于更新，不需要tid
      if (!isUpdate) {
        formData.append("tid", approvedPayment.tid.toString())
      }

      formData.append("tax_id", taxId)
      formData.append("organization", organization)
      formData.append("address", address)
      formData.append("contact_name", contactName)
      formData.append("contact_email", contactEmail)
      formData.append("contact_phone", contactPhone)
      formData.append("instruction", instruction)

      const response = await fetch(apiUrl, {
        method: method,
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
        body: formData,
      })

      const result = (await response.json()) as Record<string, unknown>

      if (response.ok && ((result.code as number) === 0 || (result.code as number) === 200)) {
        addToast({
          type: "success",
          title: "Success",
          message: isUpdate
            ? "Invoice information updated successfully!"
            : "Invoice information submitted successfully!",
        })
        // 重新获取发票信息以更新显示
        fetchInvoiceInfo()
        // 隐藏表单
        setShowInvoiceForm(false)
      } else {
        addToast({
          type: "error",
          title: "Error",
          message:
            (result.msg as string) ||
            (result.message as string) ||
            `Failed to ${isUpdate ? "update" : "submit"} invoice information`,
        })
      }
    } catch (_error) {
      addToast({
        type: "error",
        title: "Error",
        message: `An error occurred while ${existingInvoice ? "updating" : "submitting"} invoice information`,
      })
    } finally {
      // 重置提交状态
      setIsSubmittingInvoice(false)
    }
  }

  // Map user's membership type to fee type
  const getUserFeeType = (membership: string) => {
    if (!membership) return "Student" // Default fallback

    // Map membership types to fee types - 使用精确匹配避免误判
    const membershipLower = membership.toLowerCase().trim()

    // 只有明确是学生的才归类为学生费用
    if (membershipLower === "student" || membershipLower === "student member") {
      return "Student"
    } else {
      // 所有其他类型（包括 Non-Student Member, Non-Student 等）都归类为非学生费用
      return "Non-student (postdocs, sponsors, PIs and teachers)"
    }
  }

  const userFeeType = user ? getUserFeeType(user.membership || "") : "Student"

  // Calculate current fee based on date and user's registration type
  const getCurrentFee = (type: string) => {
    const fee = registrationFees.find((f) => f.type === type)
    if (!fee) return null

    // 只在客户端计算日期相关逻辑
    if (!isClient) {
      return fee.earlyBird // 服务器端默认返回早鸟价格
    }

    const now = new Date()
    const earlyDeadline = new Date("2025-08-15")

    return now <= earlyDeadline ? fee.earlyBird : fee.regular
  }

  const currentFee = getCurrentFee(userFeeType)

  if (!user) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin mb-4 text-4xl text-gray-400"></i>
          <p className="text-gray-600">Loading payment information...</p>
        </div>
      </div>
    )
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setUploadedFile(file)
      setHasNewFile(true) // 标记有新文件上传
    }
  }

  const handleSubmitPayment = async () => {
    // 防止重复提交
    if (isSubmittingPayment) {
      return
    }

    // 检查支付操作权限
    const permissions = getPaymentPermissions()

    if (!isEditMode && !permissions.canAdd) {
      addToast({
        type: "error",
        title: "Cannot Add Payment",
        message: permissions.reason,
      })
      return
    }

    if (isEditMode && !permissions.canModify) {
      addToast({
        type: "error",
        title: "Cannot Modify Payment",
        message: permissions.reason,
      })
      return
    }

    // Detailed form validation
    const validationErrors: string[] = []

    // Required field validation
    if (!region) {
      validationErrors.push("Please select payment region")
    }
    if (!method) {
      validationErrors.push("Please select payment method")
    }
    if (!transactionId || transactionId.trim() === "") {
      validationErrors.push("Please enter transaction ID")
    }
    if (!transactionTime) {
      validationErrors.push("Please select transaction time")
    }
    if (!amount || amount.trim() === "") {
      validationErrors.push("Please enter payment amount")
    }

    // Transaction ID format validation
    if (transactionId && transactionId.trim().length < 3) {
      validationErrors.push("Transaction ID must be at least 3 characters")
    }

    // Amount format validation
    if (amount) {
      const amountNum = parseFloat(amount)
      if (isNaN(amountNum) || amountNum <= 0) {
        validationErrors.push("Payment amount must be a positive number")
      }
      if (amountNum > 999999) {
        validationErrors.push("Payment amount cannot exceed 999,999")
      }
    }

    // Transaction time validation
    if (transactionTime) {
      const transactionDate = new Date(transactionTime)
      const now = new Date()
      const oneYearAgo = new Date()
      oneYearAgo.setFullYear(now.getFullYear() - 1)

      if (transactionDate > now) {
        validationErrors.push("Transaction time cannot be in the future")
      }
      if (transactionDate < oneYearAgo) {
        validationErrors.push("Transaction time cannot be more than one year ago")
      }
    }

    // For new submissions, file is required; for edit mode, only validate if new file is selected
    if (!isEditMode && !uploadedFile) {
      validationErrors.push("Please upload payment receipt")
    }

    // File size validation
    if (uploadedFile && uploadedFile.size > 10 * 1024 * 1024) {
      validationErrors.push("File size cannot exceed 10MB")
    }

    // If there are validation errors, show the first error
    if (validationErrors.length > 0) {
      addToast({
        type: "error",
        title: "Form Validation Failed",
        message: validationErrors[0] || "Form validation failed",
      })
      return
    }

    if (!user?.id) {
      addToast({
        type: "error",
        title: "User Error",
        message: "User information not available",
      })
      return
    }

    // 获取认证token
    const authToken = getAuthToken()
    if (!authToken) {
      addToast({
        type: "error",
        title: "Authentication Error",
        message: "Authentication token not found. Please login again.",
      })
      window.location.href = "/login"
      return
    }

    // 设置提交状态
    setIsSubmittingPayment(true)

    try {
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL

      // 格式化时间为 yyyy-mm-dd hh:MM:ss 格式
      const formatDateTime = (dateTimeLocal: string) => {
        if (!dateTimeLocal) return ""
        // dateTimeLocal 格式: 2025-06-01T18:41
        // 需要转换为: 2025-06-01 18:41:00
        const date = new Date(dateTimeLocal)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, "0")
        const day = String(date.getDate()).padStart(2, "0")
        const hours = String(date.getHours()).padStart(2, "0")
        const minutes = String(date.getMinutes()).padStart(2, "0")
        const seconds = String(date.getSeconds()).padStart(2, "0")

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }

      const formattedTransactionTime = formatDateTime(transactionTime)

      // 处理team_members字段逻辑
      // 合并标签和当前输入
      let allMembers = [...teamMemberTags]
      if (teamMembers.trim() !== "") {
        allMembers.push(teamMembers.trim())
      }

      // 去除重复和空值，并去除每个用户名的空格
      const cleanedMembers = allMembers.map((member) => member.replace(/\s+/g, "")).filter((member) => member !== "")
      allMembers = Array.from(new Set(cleanedMembers))

      // 确保当前用户在列表中
      if (user.username && !allMembers.includes(user.username)) {
        allMembers.push(user.username)
      }

      const processedTeamMembers = allMembers.join(",")

      // 使用 FormData 来处理文件上传
      const formData = new FormData()

      // 添加表单字段
      formData.append("region", region)
      formData.append("method", method.toString())
      formData.append("transaction_id", transactionId)
      formData.append("transaction_time", formattedTransactionTime)
      formData.append("amount", amount)
      formData.append("team_members", processedTeamMembers)
      formData.append("notes", notes)

      // 添加文件（只有在新提交模式或编辑模式下有新文件时才添加）
      if (uploadedFile && (!isEditMode || hasNewFile)) {
        formData.append("receipt", uploadedFile)
      }

      // 根据是否为编辑模式选择不同的API端点和方法
      const apiUrl =
        isEditMode && paymentRecords.length > 0
          ? `${apiBaseUrl}/api/payment/${paymentRecords[0]?.tid}`
          : `${apiBaseUrl}/api/payment/${user?.id}`

      const response = await fetch(apiUrl, {
        method: isEditMode ? "PUT" : "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          // 注意：使用 FormData 时不要设置 Content-Type，让浏览器自动设置
        },
        body: formData,
      })

      const result = (await response.json()) as Record<string, unknown>

      if (response.ok && ((result.code as number) === 0 || (result.code as number) === 200)) {
        addToast({
          type: "success",
          title: "Success",
          message: isEditMode
            ? "Payment information updated successfully!"
            : "Payment information submitted successfully!",
        })

        if (isEditMode) {
          // 编辑模式下退出编辑状态
          setIsEditMode(false)
          setHasNewFile(false) // 重置新文件标志
        } else {
          // 新提交模式下重置表单
          setShowAddForm(false) // 重置添加表单状态
          setTransactionId("")
          setTransactionTime("")
          setAmount("")
          setTeamMembers("")
          setTeamMemberTags([])
          setNotes("")
          setUploadedFile(null)
          setHasNewFile(false) // 重置新文件标志
        }

        // 重新获取支付记录
        fetchPaymentRecords()
      } else {
        addToast({
          type: "error",
          title: "Error",
          message: (result.msg as string) || (result.message as string) || "Failed to submit payment information",
        })
      }
    } catch (_error) {
      addToast({
        type: "error",
        title: "Error",
        message: "An error occurred while submitting payment information",
      })
    } finally {
      // 重置提交状态
      setIsSubmittingPayment(false)
    }
  }

  // 如果还没有客户端状态，显示简化的加载界面
  if (!isClient) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Management</h1>
          <p className="mt-1 text-gray-600">Loading payment information...</p>
        </div>
        <div className="animate-pulse">
          <div className="h-32 rounded-lg bg-gray-200"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
        <h1 className="text-2xl font-bold text-gray-900">Payment Management</h1>
        <p className="mt-1 text-gray-600">Manage your IFMB 2025 conference registration payment</p>
      </motion.div>

      {/* Payment Status Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card className="border-l-4 border-l-amber-500">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <i className="fas fa-exclamation-triangle mr-2 text-amber-500"></i>
                Payment Status
              </div>
              {paymentRecords.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (paymentRecords[0]) {
                      setSelectedRecord(paymentRecords[0])
                      setIsDetailDialogOpen(true)
                    }
                  }}
                >
                  <i className="fas fa-eye mr-2"></i>
                  View Details
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div>
                <Label className="text-sm text-gray-600">Current Status</Label>
                <div className="mt-1 flex items-center">
                  {paymentRecords.length > 0 && paymentRecords[0] ? (
                    <Badge
                      variant="outline"
                      className={
                        isPaymentApproved(paymentRecords[0])
                          ? "border-green-200 bg-green-50 text-green-700"
                          : isPaymentReviewed(paymentRecords[0])
                            ? "border-red-200 bg-red-50 text-red-700"
                            : "border-amber-200 bg-amber-50 text-amber-700"
                      }
                    >
                      <i
                        className={`mr-1 ${
                          isPaymentApproved(paymentRecords[0])
                            ? "fas fa-check"
                            : isPaymentReviewed(paymentRecords[0])
                              ? "fas fa-times"
                              : "fas fa-clock"
                        }`}
                      ></i>
                      {isPaymentApproved(paymentRecords[0])
                        ? "Approved"
                        : isPaymentReviewed(paymentRecords[0])
                          ? "Rejected"
                          : "Pending"}
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="border-gray-200 bg-gray-50 text-gray-700">
                      <i className="fas fa-info mr-1"></i>
                      Not Submitted
                    </Badge>
                  )}
                </div>
              </div>
              <div>
                <Label className="text-sm text-gray-600">Registration Type</Label>
                <div className="mt-1 flex items-center">
                  <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-700">
                    {user.membership || "Not specified"}
                  </Badge>
                </div>
              </div>
              <div>
                <Label className="text-sm text-gray-600">Early Bird Status</Label>
                <div className="mt-1 flex items-center">
                  {isEarlyBird ? (
                    <Badge className="bg-green-100 text-green-800">
                      <i className="fas fa-check mr-1"></i>
                      Eligible for Early Bird
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-gray-50 text-gray-700">
                      <i className="fas fa-times mr-1"></i>
                      Regular Rate
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {paymentRecords.length > 0 && paymentRecords[0] && (
              <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm text-blue-700">Latest Payment Record</Label>
                    <p className="mt-1 text-sm text-blue-800">
                      Amount: ¥{paymentRecords[0].amount} • Submitted:{" "}
                      {isClient ? formatDate(paymentRecords[0].t_create_time) : ""} • Transaction ID:{" "}
                      {paymentRecords[0].transaction_id}
                    </p>
                  </div>
                  <Badge
                    variant="outline"
                    className={
                      isPaymentApproved(paymentRecords[0])
                        ? "border-green-200 bg-green-50 text-green-700"
                        : isPaymentReviewed(paymentRecords[0])
                          ? "border-red-200 bg-red-50 text-red-700"
                          : "border-amber-200 bg-amber-50 text-amber-700"
                    }
                  >
                    {isPaymentApproved(paymentRecords[0])
                      ? "Approved"
                      : isPaymentReviewed(paymentRecords[0])
                        ? "Rejected"
                        : "Pending"}
                  </Badge>
                </div>

                {/* 添加审核信息显示 */}
                {isPaymentReviewed(paymentRecords[0]) && (
                  <div
                    className={`mt-3 border-t pt-3 ${
                      isPaymentApproved(paymentRecords[0]) ? "border-green-200" : "border-red-200"
                    }`}
                  >
                    <Label
                      className={`text-sm ${isPaymentApproved(paymentRecords[0]) ? "text-green-700" : "text-red-700"}`}
                    >
                      Review Information
                    </Label>
                    <p
                      className={`mt-1 text-sm ${
                        isPaymentApproved(paymentRecords[0]) ? "text-green-800" : "text-red-800"
                      }`}
                    >
                      Review Status: {isPaymentApproved(paymentRecords[0]) ? "Approved" : "Rejected"} • Review Time:{" "}
                      {isClient && paymentRecords[0].review_time !== "0001-01-01T00:00:00Z"
                        ? formatDate(paymentRecords[0].review_time, { includeTime: true })
                        : "Unknown"}{" "}
                      • Reviewer:{" "}
                      {paymentRecords[0].review_user ||
                        paymentRecords[0].review_username ||
                        `ID: ${paymentRecords[0].reviewer}` ||
                        "Not specified"}
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      <Tabs defaultValue="calculator" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="calculator">Fee Calculator</TabsTrigger>
          <TabsTrigger value="payment">Payment Info</TabsTrigger>
          <TabsTrigger value="upload">Upload Receipt</TabsTrigger>
          <TabsTrigger value="invoice">Invoice Info</TabsTrigger>
        </TabsList>

        {/* Fee Calculator Tab */}
        <TabsContent value="calculator" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
            <Card>
              <CardHeader>
                <CardTitle>Registration Fee Calculator</CardTitle>
                <CardDescription>
                  Calculate your conference registration fee based on your participant type
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm text-blue-700">Your Registration Type</Label>
                      <p className="mt-1 font-medium text-blue-900">{user.membership || "Not specified"}</p>
                    </div>
                    <div>
                      <Label className="text-sm text-blue-700">Fee Category</Label>
                      <p className="mt-1 font-medium text-blue-900">{userFeeType}</p>
                    </div>
                  </div>
                </div>

                {currentFee && (
                  <div className="rounded-lg bg-gray-50 p-6">
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                      <div className="rounded-lg border bg-white p-4 text-center">
                        <div className="mb-2 text-sm text-gray-600">Early Bird Rate</div>
                        <div className="text-2xl font-bold text-green-600">
                          ¥{registrationFees.find((f) => f.type === userFeeType)?.earlyBird.price}
                        </div>
                        <div className="mt-1 text-xs text-gray-500">Until August 15, 2025</div>
                      </div>
                      <div className="rounded-lg border bg-white p-4 text-center">
                        <div className="mb-2 text-sm text-gray-600">Regular Rate</div>
                        <div className="text-2xl font-bold text-gray-600">
                          ¥{registrationFees.find((f) => f.type === userFeeType)?.regular.price}
                        </div>
                        <div className="mt-1 text-xs text-gray-500">After August 15, 2025</div>
                      </div>
                    </div>

                    <div className="mt-6 rounded-lg border border-blue-200 bg-blue-50 p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-blue-900">Your Current Fee</div>
                          <div className="text-sm text-blue-700">
                            {isEarlyBird ? "Early Bird Rate" : "Regular Rate"}
                          </div>
                        </div>
                        <div className="text-3xl font-bold text-blue-900">¥{currentFee.price}</div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                  <h4 className="mb-2 font-medium text-green-900">Registration Fee Includes:</h4>
                  <ul className="space-y-1 text-sm text-green-800">
                    <li className="flex items-center">
                      <i className="fas fa-check mr-2 text-green-600"></i>
                      Conference materials and abstract compendium
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check mr-2 text-green-600"></i>
                      Coffee breaks during the conference
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check mr-2 text-green-600"></i>
                      Conference meals (lunch and dinner)
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check mr-2 text-green-600"></i>
                      Access to all conference sessions
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check mr-2 text-green-600"></i>
                      Networking events and social activities
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        {/* Payment Information Tab */}
        <TabsContent value="payment" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
            <Card>
              <CardHeader>
                <CardTitle>Bank Transfer Information</CardTitle>
                <CardDescription>Transfer funds to Huazhong Agricultural University account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label>Select Payment Method</Label>
                  <div className="mt-2 grid grid-cols-1 gap-4 md:grid-cols-3">
                    <button
                      onClick={() => setPaymentMethod("international")}
                      className={`rounded-lg border p-4 text-left transition-colors ${
                        paymentMethod === "international"
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-center">
                        <i className="fas fa-globe mr-3 text-blue-600"></i>
                        <div>
                          <div className="font-medium">International Transfer</div>
                          <div className="text-sm text-gray-500">For overseas participants</div>
                        </div>
                      </div>
                    </button>
                    <button
                      onClick={() => setPaymentMethod("domestic")}
                      className={`rounded-lg border p-4 text-left transition-colors ${
                        paymentMethod === "domestic"
                          ? "border-green-500 bg-green-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-center">
                        <i className="fas fa-map-marker-alt mr-3 text-green-600"></i>
                        <div>
                          <div className="font-medium">Domestic Transfer</div>
                          <div className="text-sm text-gray-500">For Chinese participants</div>
                        </div>
                      </div>
                    </button>
                    <button
                      onClick={() => setPaymentMethod("alipay")}
                      className={`rounded-lg border p-4 text-left transition-colors ${
                        paymentMethod === "alipay"
                          ? "border-orange-500 bg-orange-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-center">
                        <i className="fas fa-mobile-alt mr-3 text-orange-600"></i>
                        <div>
                          <div className="font-medium">Alipay Payment</div>
                          <div className="text-sm text-gray-500">Mobile payment via QR code</div>
                        </div>
                      </div>
                    </button>
                  </div>
                </div>

                {paymentMethod === "international" && (
                  <div className="rounded-lg border border-blue-200 bg-blue-50 p-6">
                    <h4 className="mb-4 font-medium text-blue-900">International Wire Transfer Details</h4>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm text-blue-700">Beneficiary</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">
                          {paymentInfo.international.beneficiary}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm text-blue-700">Bank</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">
                          {paymentInfo.international.bank}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm text-blue-700">Account Number</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">
                          {paymentInfo.international.accountNumber}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm text-blue-700">SWIFT Code</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">
                          {paymentInfo.international.swiftCode}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm text-blue-700">Bank Address</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">
                          {paymentInfo.international.address}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {paymentMethod === "domestic" && (
                  <div className="rounded-lg border border-green-200 bg-green-50 p-6">
                    <h4 className="mb-4 font-medium text-green-900">Domestic Bank Transfer Details</h4>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm text-green-700">Beneficiary</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">
                          {paymentInfo.domestic.beneficiary}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm text-green-700">Bank</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">{paymentInfo.domestic.bank}</p>
                      </div>
                      <div>
                        <Label className="text-sm text-green-700">Beneficiary Number</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">
                          {paymentInfo.domestic.beneficiaryNumber}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm text-green-700">Account</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">{paymentInfo.domestic.account}</p>
                      </div>
                      <div>
                        <Label className="text-sm text-green-700">Address</Label>
                        <p className="rounded border bg-white p-2 font-mono text-sm">{paymentInfo.domestic.address}</p>
                      </div>
                    </div>
                  </div>
                )}

                {paymentMethod === "alipay" && (
                  <div className="rounded-lg border border-orange-200 bg-orange-50 p-6">
                    <h4 className="mb-4 font-medium text-orange-900">Alipay Payment Details</h4>
                    <div className="text-center">
                      <p className="mb-4 text-sm text-orange-800">Scan the QR code below to pay with Alipay</p>
                      <div className="mx-auto mb-4 max-w-64">
                        <img
                          src="https://minioapi.bugmaker.me/ifmb-2025-public/支付宝.jpg"
                          alt="Alipay QR Code"
                          className="w-full rounded-lg border border-orange-200 shadow-sm"
                        />
                      </div>
                      <div className="rounded border border-orange-300 bg-white p-3">
                        <p className="text-sm text-orange-800">
                          <strong>Important:</strong> Please include your name and "IFMB2025" in the payment note
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
                  <h4 className="mb-2 font-medium text-amber-900">
                    <i className="fas fa-exclamation-triangle mr-2"></i>
                    Important Payment Instructions
                  </h4>
                  <ul className="space-y-1 text-sm text-amber-800">
                    <li>
                      • Please indicate "<strong>Name - Affiliation - IFMB2025</strong>" when making the payment
                    </li>
                    <li>• After successful payment, upload your payment voucher in the "Upload Receipt" tab</li>
                    <li>• For multiple attendees from the same organization, attach names of all participants</li>
                    <li>• Payment confirmation is based on the time of remittance or transfer</li>
                    <li>• Conference fee invoices will be sent to your registered email during the conference</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        {/* Upload Receipt Tab */}
        <TabsContent value="upload" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
            {paymentRecords.length > 0 && paymentRecords[0] && !isEditMode && !showAddForm ? (
              // 显示已有支付信息
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <i className="fas fa-receipt mr-2 text-blue-600"></i>
                      Submitted Payment Information
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => paymentRecords[0] && handleEditPayment(paymentRecords[0])}
                      disabled={!getPaymentPermissions().canModify}
                    >
                      <i className="fas fa-edit mr-2"></i>
                      Edit Information
                    </Button>
                  </CardTitle>
                  <CardDescription>Your submitted payment record information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 支付状态 */}
                  <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm text-blue-700">Payment Status</Label>
                        <div className="mt-1">
                          <Badge
                            variant="outline"
                            className={
                              isPaymentApproved(paymentRecords[0])
                                ? "border-green-200 bg-green-50 text-green-700"
                                : isPaymentReviewed(paymentRecords[0])
                                  ? "border-red-200 bg-red-50 text-red-700"
                                  : "border-amber-200 bg-amber-50 text-amber-700"
                            }
                          >
                            <i
                              className={`mr-1 ${
                                isPaymentApproved(paymentRecords[0])
                                  ? "fas fa-check"
                                  : isPaymentReviewed(paymentRecords[0])
                                    ? "fas fa-times"
                                    : "fas fa-clock"
                              }`}
                            ></i>
                            {isPaymentApproved(paymentRecords[0])
                              ? "Approved"
                              : isPaymentReviewed(paymentRecords[0])
                                ? "Rejected"
                                : "Pending"}
                          </Badge>
                        </div>
                      </div>
                      <div>
                        <Label className="text-sm text-blue-700">Payment Amount</Label>
                        <p className="mt-1 text-xl font-semibold text-blue-900">¥{paymentRecords[0].amount}</p>
                      </div>
                    </div>
                  </div>

                  {/* 支付详情 */}
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <Label className="text-sm text-gray-600">Payment Region</Label>
                      <p className="mt-1 rounded border bg-gray-50 p-2 text-sm">
                        {paymentRecords[0].region === "domestic" ? "Domestic Payment" : "International Payment"}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm text-gray-600">Payment Method</Label>
                      <p className="mt-1 rounded border bg-gray-50 p-2 text-sm">{paymentRecords[0].method_name}</p>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm text-gray-600">Transaction ID</Label>
                    <p className="mt-1 rounded border bg-gray-50 p-2 font-mono text-sm">
                      {paymentRecords[0].transaction_id}
                    </p>
                  </div>

                  <div>
                    <Label className="text-sm text-gray-600">Transaction Time</Label>
                    <p className="mt-1 rounded border bg-gray-50 p-2 text-sm">
                      {isClient ? formatDate(paymentRecords[0].transaction_time, { includeTime: true }) : ""}
                    </p>
                  </div>

                  {paymentRecords[0].team_users && (
                    <div>
                      <Label className="text-sm text-gray-600">Team Members</Label>
                      <p className="mt-1 rounded border bg-gray-50 p-2 text-sm">{paymentRecords[0].team_users}</p>
                    </div>
                  )}

                  {paymentRecords[0].notes && (
                    <div>
                      <Label className="text-sm text-gray-600">Notes</Label>
                      <p className="mt-1 rounded border bg-gray-50 p-2 text-sm">{paymentRecords[0].notes}</p>
                    </div>
                  )}

                  {/* 审核信息显示 */}
                  {isPaymentReviewed(paymentRecords[0]) && (
                    <div
                      className={`rounded-lg border p-4 ${
                        isPaymentApproved(paymentRecords[0])
                          ? "border-green-200 bg-green-50"
                          : "border-red-200 bg-red-50"
                      }`}
                    >
                      <div className="flex items-start">
                        <i
                          className={`mt-0.5 mr-3 ${
                            isPaymentApproved(paymentRecords[0])
                              ? "fas fa-check-circle text-green-600"
                              : "fas fa-exclamation-triangle text-red-600"
                          }`}
                        ></i>
                        <div>
                          <h4
                            className={`font-medium ${
                              isPaymentApproved(paymentRecords[0]) ? "text-green-900" : "text-red-900"
                            }`}
                          >
                            Review Information
                          </h4>
                          <div
                            className={`mt-2 space-y-1 text-sm ${
                              isPaymentApproved(paymentRecords[0]) ? "text-green-800" : "text-red-800"
                            }`}
                          >
                            <p>Review Status: {isPaymentApproved(paymentRecords[0]) ? "Approved" : "Rejected"}</p>
                            <p>
                              Review Time:{" "}
                              {isClient && paymentRecords[0].review_time !== "0001-01-01T00:00:00Z"
                                ? formatDate(paymentRecords[0].review_time, { includeTime: true })
                                : "Unknown"}
                            </p>
                            <p>
                              Reviewer:{" "}
                              {paymentRecords[0].review_user ||
                                paymentRecords[0].review_username ||
                                `ID: ${paymentRecords[0].reviewer}` ||
                                "Not specified"}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 支付凭证 */}
                  {paymentRecords[0].evidence && (
                    <div>
                      <Label className="text-sm text-gray-600">Payment Receipt</Label>
                      <div className="mt-2 rounded border bg-gray-50 p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <i className="fas fa-file-image mr-2 text-blue-600"></i>
                            <span className="text-sm">Payment receipt uploaded</span>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => paymentRecords[0]?.evidence && handleViewReceipt(paymentRecords[0].evidence)}
                          >
                            <i className={`${getFileIcon(paymentRecords[0]?.evidence || "")} mr-2`}></i>
                            View Receipt
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 根据支付状态显示不同的提示和操作 */}
                  {(() => {
                    const permissions = getPaymentPermissions()
                    if (permissions.canAdd) {
                      // 被拒绝的支付，允许添加新支付
                      return (
                        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start">
                              <i className="fas fa-plus-circle mt-0.5 mr-3 text-blue-600"></i>
                              <div>
                                <h4 className="font-medium text-blue-900">Submit New Payment</h4>
                                <p className="mt-1 text-sm text-blue-800">
                                  Your previous payment was rejected. You can submit a new payment record.
                                </p>
                              </div>
                            </div>
                            <Button
                              variant="outline"
                              className="border-blue-300 text-blue-700 hover:bg-blue-100"
                              onClick={() => {
                                // 重置表单并切换到添加模式
                                setIsEditMode(false)
                                setShowAddForm(true)
                                setRegion("")
                                setMethod(1)
                                setTransactionId("")
                                setTransactionTime("")
                                setAmount("")
                                setTeamMembers("")
                                setTeamMemberTags([])
                                setNotes("")
                                setUploadedFile(null)
                                setHasNewFile(false)
                              }}
                            >
                              <i className="fas fa-plus mr-2"></i>
                              Add New Payment
                            </Button>
                          </div>
                        </div>
                      )
                    } else {
                      // 其他情况显示原来的提示
                      return (
                        <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
                          <div className="flex items-start">
                            <i className="fas fa-info-circle mt-0.5 mr-3 text-amber-600"></i>
                            <div>
                              <h4 className="font-medium text-amber-900">Notice</h4>
                              <p className="mt-1 text-sm text-amber-800">
                                To modify payment information, click the "Edit Information" button in the top right
                                corner. Modified information will be resubmitted for review.
                              </p>
                            </div>
                          </div>
                        </div>
                      )
                    }
                  })()}
                </CardContent>
              </Card>
            ) : (
              // 显示支付表单（新提交或编辑模式）
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <i className="fas fa-upload mr-2 text-blue-600"></i>
                      {isEditMode ? "Edit Payment Information" : "Upload Payment Receipt"}
                    </div>
                    {isEditMode && (
                      <Button variant="outline" onClick={handleCancelEdit}>
                        <i className="fas fa-times mr-2"></i>
                        Cancel Edit
                      </Button>
                    )}
                    {showAddForm && paymentRecords.length > 0 && (
                      <Button variant="outline" onClick={() => setShowAddForm(false)}>
                        <i className="fas fa-arrow-left mr-2"></i>
                        Back to Payment Records
                      </Button>
                    )}
                  </CardTitle>
                  <CardDescription>
                    {isEditMode
                      ? "Modify your payment record information"
                      : "Upload your bank transfer receipt or payment confirmation"}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 权限检查提示 */}
                  {(() => {
                    const permissions = getPaymentPermissions()
                    if (!isEditMode && !permissions.canAdd) {
                      return (
                        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                          <div className="flex items-start">
                            <i className="fas fa-exclamation-triangle mt-0.5 mr-3 text-red-600"></i>
                            <div>
                              <h4 className="font-medium text-red-900">Cannot Add New Payment</h4>
                              <p className="mt-1 text-sm text-red-800">{permissions.reason}</p>
                            </div>
                          </div>
                        </div>
                      )
                    }
                    if (isEditMode && !permissions.canModify) {
                      return (
                        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                          <div className="flex items-start">
                            <i className="fas fa-exclamation-triangle mt-0.5 mr-3 text-red-600"></i>
                            <div>
                              <h4 className="font-medium text-red-900">Cannot Modify Payment</h4>
                              <p className="mt-1 text-sm text-red-800">{permissions.reason}</p>
                            </div>
                          </div>
                        </div>
                      )
                    }
                    return null
                  })()}
                  {/* Region Selection */}
                  <div>
                    <Label htmlFor="region">Payment Region</Label>
                    <div className="mt-2 grid grid-cols-1 gap-4 md:grid-cols-2">
                      <button
                        onClick={() => setRegion("domestic")}
                        className={`rounded-lg border p-4 text-left transition-colors ${
                          region === "domestic" ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        <div className="font-medium">Domestic</div>
                        <div className="text-sm text-gray-600">For payments within China</div>
                      </button>
                      <button
                        onClick={() => setRegion("international")}
                        className={`rounded-lg border p-4 text-left transition-colors ${
                          region === "international"
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        <div className="font-medium">International</div>
                        <div className="text-sm text-gray-600">For payments outside China</div>
                      </button>
                    </div>
                  </div>

                  {/* Payment Method */}
                  <div>
                    <Label htmlFor="method">Payment Method</Label>
                    <div className="mt-2 grid grid-cols-1 gap-4 md:grid-cols-2">
                      <button
                        onClick={() => setMethod(1)}
                        className={`rounded-lg border p-4 text-left transition-colors ${
                          method === 1 ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        <div className="font-medium">Bank Transfer</div>
                        <div className="text-sm text-gray-600">Wire transfer or bank deposit</div>
                      </button>
                      <button
                        onClick={() => setMethod(2)}
                        className={`rounded-lg border p-4 text-left transition-colors ${
                          method === 2 ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        <div className="font-medium">AliPay</div>
                        <div className="text-sm text-gray-600">Alipay mobile payment</div>
                      </button>
                    </div>
                  </div>

                  {/* Team Members */}
                  <div>
                    <Label htmlFor="team-members">Team Members (Optional)</Label>
                    <p className="mt-1 text-xs text-amber-600">
                      <i className="fas fa-info-circle mr-1"></i>
                      Note: Team members must be registered users with valid usernames
                    </p>

                    {/* 显示已添加的团队成员标签 */}
                    {teamMemberTags.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-2">
                        {teamMemberTags.map((member, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-sm text-blue-800"
                          >
                            <span>{member}</span>
                            <button
                              type="button"
                              onClick={() => removeTeamMember(member)}
                              className="ml-1 text-blue-600 hover:text-blue-800"
                            >
                              <i className="fas fa-times text-xs"></i>
                            </button>
                          </div>
                        ))}
                      </div>
                    )}

                    <Input
                      id="team-members"
                      placeholder="Enter username and press comma or Enter to add"
                      value={teamMembers}
                      onChange={(e) => handleTeamMembersChange(e.target.value)}
                      onBlur={handleTeamMembersBlur}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault()
                          handleTeamMembersBlur()
                        }
                      }}
                      className={`mt-2 ${fieldErrors.teamMembers ? "border-red-500" : ""}`}
                    />
                    {fieldErrors.teamMembers ? (
                      <p className="mt-1 text-xs text-red-500">
                        <i className="fas fa-exclamation-circle mr-1"></i>
                        {fieldErrors.teamMembers}
                      </p>
                    ) : (
                      <p className="mt-1 text-xs text-gray-500">
                        Enter usernames one by one. Press comma, Enter, or click away to add each username as a tag.
                        Spaces will be automatically removed.
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="payment-receipt">
                      Payment Receipt/Voucher
                      {!isEditMode && <span className="text-red-500">*</span>}
                    </Label>
                    {isEditMode && (
                      <p className="mt-1 text-xs text-blue-600">
                        <i className="fas fa-info-circle mr-1"></i>
                        You already have a receipt uploaded. Only upload a new file if you want to replace it.
                      </p>
                    )}
                    <div className="mt-2">
                      <div className="flex w-full items-center justify-center">
                        <label
                          htmlFor="payment-receipt"
                          className="flex h-64 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                          <div className="flex flex-col items-center justify-center pt-5 pb-6">
                            <i className="fas fa-cloud-upload-alt mb-4 text-4xl text-gray-400"></i>
                            <p className="mb-2 text-sm text-gray-500">
                              <span className="font-semibold">Click to upload</span> or drag and drop
                            </p>
                            <p className="text-xs text-gray-500">PNG, JPG, PDF up to 10MB</p>
                          </div>
                          <input
                            id="payment-receipt"
                            type="file"
                            className="hidden"
                            accept=".png,.jpg,.jpeg,.pdf"
                            onChange={handleFileUpload}
                          />
                        </label>
                      </div>
                      {uploadedFile && (
                        <div className="mt-4 rounded-lg border border-green-200 bg-green-50 p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <i className="fas fa-file mr-3 text-green-600"></i>
                              <div>
                                <p className="font-medium text-green-900">{uploadedFile.name}</p>
                                <p className="text-sm text-green-700">
                                  {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              </div>
                            </div>
                            {isEditMode && hasNewFile && (
                              <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-700">
                                <i className="fas fa-plus mr-1"></i>
                                New File
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="transaction-id">
                      Transaction ID <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="transaction-id"
                      placeholder="Enter your transaction reference number"
                      value={transactionId}
                      onChange={(e) => {
                        setTransactionId(e.target.value)
                        updateFieldError("transactionId", e.target.value)
                      }}
                      className={`mt-2 ${fieldErrors.transactionId ? "border-red-500" : ""}`}
                      required
                    />
                    {fieldErrors.transactionId && (
                      <p className="mt-1 text-xs text-red-500">
                        <i className="fas fa-exclamation-circle mr-1"></i>
                        {fieldErrors.transactionId}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="transaction-time">
                      Transaction Date & Time <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="transaction-time"
                      type="datetime-local"
                      value={transactionTime}
                      onChange={(e) => {
                        setTransactionTime(e.target.value)
                        updateFieldError("transactionTime", e.target.value)
                      }}
                      className={`mt-2 ${fieldErrors.transactionTime ? "border-red-500" : ""}`}
                      required
                    />
                    {fieldErrors.transactionTime ? (
                      <p className="mt-1 text-xs text-red-500">
                        <i className="fas fa-exclamation-circle mr-1"></i>
                        {fieldErrors.transactionTime}
                      </p>
                    ) : (
                      <p className="mt-1 text-xs text-gray-500">
                        Select the exact date and time when you made the payment
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="amount">
                      Payment Amount (CNY) <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      placeholder="Enter the amount you paid"
                      value={amount}
                      onChange={(e) => {
                        setAmount(e.target.value)
                        updateFieldError("amount", e.target.value)
                      }}
                      className={`mt-2 ${fieldErrors.amount ? "border-red-500" : ""}`}
                      required
                    />
                    {fieldErrors.amount && (
                      <p className="mt-1 text-xs text-red-500">
                        <i className="fas fa-exclamation-circle mr-1"></i>
                        {fieldErrors.amount}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="notes">Additional Notes</Label>
                    <Textarea
                      id="notes"
                      placeholder="Any additional information about your payment..."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="mt-2"
                      rows={3}
                    />
                  </div>

                  <Button
                    className="w-full"
                    onClick={handleSubmitPayment}
                    disabled={(() => {
                      const permissions = getPaymentPermissions()
                      return isSubmittingPayment || (isEditMode ? !permissions.canModify : !permissions.canAdd)
                    })()}
                  >
                    <i
                      className={`mr-2 ${isSubmittingPayment ? "fas fa-spinner fa-spin" : isEditMode ? "fas fa-save" : "fas fa-upload"}`}
                    ></i>
                    {isSubmittingPayment ? "Submitting..." : isEditMode ? "Save Changes" : "Submit Payment Information"}
                  </Button>
                </CardContent>
              </Card>
            )}
          </motion.div>
        </TabsContent>

        {/* Invoice Information Tab */}
        <TabsContent value="invoice" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
            <Card>
              <CardHeader>
                <CardTitle>Invoice Information</CardTitle>
                <CardDescription>Provide your invoicing details for the conference fee receipt</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 加载状态 */}
                {isInvoiceLoading && (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-center">
                      <i className="fas fa-spinner fa-spin mb-2 text-2xl text-gray-400"></i>
                      <p className="text-gray-600">Loading invoice information...</p>
                    </div>
                  </div>
                )}

                {/* 现有发票信息显示 */}
                {!isInvoiceLoading && existingInvoice && (
                  <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start">
                        <i className="fas fa-check-circle mt-0.5 mr-3 text-green-600"></i>
                        <div>
                          <h4 className="font-medium text-green-900">Invoice Information Submitted</h4>
                          <p className="mt-1 text-sm text-green-800">
                            Your invoice information has been submitted successfully
                            {isClient && existingInvoice.create_time ? (
                              <> on {formatDate(existingInvoice.create_time)}</>
                            ) : (
                              ""
                            )}
                            .
                          </p>
                          <div className="mt-3 space-y-1 text-sm text-green-800">
                            <p>
                              <strong>Organization:</strong> {existingInvoice.organization}
                            </p>
                            <p>
                              <strong>Tax ID:</strong> {existingInvoice.tax_id}
                            </p>
                            <p>
                              <strong>Contact:</strong> {existingInvoice.contact_name} ({existingInvoice.contact_email})
                            </p>
                            <p>
                              <strong>Phone:</strong> {existingInvoice.contact_phone}
                            </p>
                            <p>
                              <strong>Address:</strong> {existingInvoice.address}
                            </p>
                            {existingInvoice.instruction && (
                              <p>
                                <strong>Instructions:</strong> {existingInvoice.instruction}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-green-300 text-green-700 hover:bg-green-100"
                          onClick={handleEditInvoice}
                        >
                          <i className="fas fa-edit mr-2"></i>
                          Edit
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* 支付状态检查 */}
                {!isInvoiceLoading &&
                (paymentRecords.length === 0 || !paymentRecords.some((record) => isPaymentApproved(record))) ? (
                  <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
                    <div className="flex items-start">
                      <i className="fas fa-exclamation-triangle mt-0.5 mr-3 text-amber-600"></i>
                      <div>
                        <h4 className="font-medium text-amber-900">Payment Required</h4>
                        <p className="mt-1 text-sm text-amber-800">
                          You must have an approved payment before you can submit invoice information. Please complete
                          your payment and wait for approval first.
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  !isInvoiceLoading &&
                  showInvoiceForm && (
                    <>
                      <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start">
                            <i className="fas fa-check-circle mt-0.5 mr-3 text-green-600"></i>
                            <div>
                              <h4 className="font-medium text-green-900">Payment Approved</h4>
                              <p className="mt-1 text-sm text-green-800">
                                Your payment has been approved. You can {existingInvoice ? "update" : "submit"} your
                                invoice information.
                              </p>
                            </div>
                          </div>
                          {existingInvoice && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleCancelInvoiceEdit}
                              className="text-gray-600 hover:text-gray-800"
                            >
                              <i className="fas fa-times mr-2"></i>
                              Cancel
                            </Button>
                          )}
                        </div>
                      </div>

                      <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                        <div className="flex items-start">
                          <i className="fas fa-info-circle mt-0.5 mr-3 text-blue-600"></i>
                          <div>
                            <h4 className="font-medium text-blue-900">Invoice Information</h4>
                            <p className="mt-1 text-sm text-blue-800">
                              Conference fee invoices will be issued and sent to your registered email address during
                              the conference. Please provide accurate invoicing information below.
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Transaction ID Display */}
                      <div>
                        <Label htmlFor="invoice-transaction-id">Transaction ID</Label>
                        <Input
                          id="invoice-transaction-id"
                          value={(() => {
                            const approvedPayment = paymentRecords.find((record) => isPaymentApproved(record))
                            return approvedPayment?.transaction_id || ""
                          })()}
                          className="mt-2 bg-gray-50 text-gray-700"
                          readOnly
                          disabled
                        />
                        <p className="mt-1 text-xs text-gray-500">
                          This is the transaction ID from your approved payment
                        </p>
                      </div>

                      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div>
                          <Label htmlFor="invoice-name">
                            Organization/Company <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="invoice-name"
                            value={invoiceData.organization}
                            onChange={(e) => setInvoiceData((prev) => ({ ...prev, organization: e.target.value }))}
                            placeholder="Enter company or organization name"
                            className="mt-2"
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="invoice-tax-id">
                            Tax ID/Registration Number <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="invoice-tax-id"
                            value={invoiceData.taxId}
                            onChange={(e) => setInvoiceData((prev) => ({ ...prev, taxId: e.target.value }))}
                            placeholder="Enter tax ID or registration number"
                            className="mt-2"
                            required
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="invoice-address">
                          Invoice Address <span className="text-red-500">*</span>
                        </Label>
                        <Textarea
                          id="invoice-address"
                          value={invoiceData.address}
                          onChange={(e) => setInvoiceData((prev) => ({ ...prev, address: e.target.value }))}
                          placeholder="Enter complete billing address..."
                          className="mt-2"
                          rows={3}
                          required
                        />
                      </div>

                      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div>
                          <Label htmlFor="invoice-contact">
                            Contact Person <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="invoice-contact"
                            value={invoiceData.contactName}
                            onChange={(e) => setInvoiceData((prev) => ({ ...prev, contactName: e.target.value }))}
                            placeholder="Enter contact person name"
                            className="mt-2"
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="invoice-email">
                            Invoice Email <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="invoice-email"
                            type="email"
                            value={invoiceData.contactEmail}
                            onChange={(e) => setInvoiceData((prev) => ({ ...prev, contactEmail: e.target.value }))}
                            placeholder="Enter email for invoice delivery"
                            className="mt-2"
                            required
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="invoice-phone">
                          Contact Phone <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="invoice-phone"
                          value={invoiceData.contactPhone}
                          onChange={(e) => setInvoiceData((prev) => ({ ...prev, contactPhone: e.target.value }))}
                          placeholder="Enter contact phone number"
                          className="mt-2"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="invoice-notes">
                          Special Instructions <span className="text-red-500">*</span>
                        </Label>
                        <Textarea
                          id="invoice-notes"
                          value={invoiceData.instruction}
                          onChange={(e) => setInvoiceData((prev) => ({ ...prev, instruction: e.target.value }))}
                          placeholder="Any special requirements for the invoice..."
                          className="mt-2"
                          rows={3}
                          required
                        />
                      </div>

                      <Button className="w-full" onClick={handleSubmitInvoice} disabled={isSubmittingInvoice}>
                        <i className={`mr-2 ${isSubmittingInvoice ? "fas fa-spinner fa-spin" : "fas fa-save"}`}></i>
                        {isSubmittingInvoice
                          ? "Submitting..."
                          : existingInvoice
                            ? "Update Invoice Information"
                            : "Submit Invoice Information"}
                      </Button>
                    </>
                  )
                )}
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>
      </Tabs>

      {/* 支付记录详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Payment Record Details</DialogTitle>
            <DialogDescription>View detailed payment record information</DialogDescription>
          </DialogHeader>

          {selectedRecord && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <Label className="text-sm text-gray-600">Payment Status</Label>
                  <div className="mt-1">
                    <Badge
                      variant="outline"
                      className={
                        isPaymentApproved(selectedRecord)
                          ? "border-green-200 bg-green-50 text-green-700"
                          : isPaymentReviewed(selectedRecord)
                            ? "border-red-200 bg-red-50 text-red-700"
                            : "border-amber-200 bg-amber-50 text-amber-700"
                      }
                    >
                      <i
                        className={`mr-1 ${
                          isPaymentApproved(selectedRecord)
                            ? "fas fa-check"
                            : isPaymentReviewed(selectedRecord)
                              ? "fas fa-times"
                              : "fas fa-clock"
                        }`}
                      ></i>
                      {isPaymentApproved(selectedRecord)
                        ? "Approved"
                        : isPaymentReviewed(selectedRecord)
                          ? "Rejected"
                          : "Pending"}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Payment Amount</Label>
                  <p className="mt-1 text-lg font-semibold">¥{selectedRecord.amount}</p>
                </div>
              </div>

              {/* 交易信息 */}
              <div className="space-y-4">
                <div>
                  <Label className="text-sm text-gray-600">Transaction ID</Label>
                  <p className="mt-1 rounded border bg-gray-50 p-2 font-mono text-sm">
                    {selectedRecord.transaction_id}
                  </p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Transaction Time</Label>
                  <p className="mt-1 text-sm">
                    {isClient ? formatDate(selectedRecord.transaction_time, { includeTime: true }) : ""}
                  </p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Payment Region</Label>
                  <p className="mt-1 text-sm">{selectedRecord.region === "domestic" ? "Domestic" : "International"}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Payment Method</Label>
                  <p className="mt-1 text-sm">{selectedRecord.method_name}</p>
                </div>
              </div>

              {/* 团队成员 */}
              {selectedRecord.team_users && (
                <div>
                  <Label className="text-sm text-gray-600">Team Members</Label>
                  <p className="mt-1 rounded border bg-gray-50 p-2 text-sm">{selectedRecord.team_users}</p>
                </div>
              )}

              {/* 备注 */}
              {selectedRecord.notes && (
                <div>
                  <Label className="text-sm text-gray-600">Notes</Label>
                  <p className="mt-1 rounded border bg-gray-50 p-2 text-sm">{selectedRecord.notes}</p>
                </div>
              )}

              {/* 审核信息 */}
              {selectedRecord.review_id > 0 && (
                <div className="border-t pt-4">
                  <h4 className="mb-2 font-medium text-gray-900">Review Information</h4>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <Label className="text-sm text-gray-600">Review Time</Label>
                      <p className="mt-1 text-sm">
                        {isClient && selectedRecord.review_time !== "0001-01-01T00:00:00Z"
                          ? formatDate(selectedRecord.review_time, { includeTime: true })
                          : "Unknown"}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm text-gray-600">Reviewer</Label>
                      <p className="mt-1 text-sm">
                        {selectedRecord.review_username ||
                          selectedRecord.review_user ||
                          `ID: ${selectedRecord.reviewer}` ||
                          "Not specified"}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex justify-between border-t pt-4">
                <div>
                  <p className="text-xs text-gray-500">
                    Submitted: {isClient ? formatDate(selectedRecord.t_create_time, { includeTime: true }) : ""}
                  </p>
                </div>
                <div className="flex space-x-2">
                  {selectedRecord.evidence && (
                    <Button variant="outline" size="sm" onClick={() => handleViewReceipt(selectedRecord.evidence)}>
                      <i className={`${getFileIcon(selectedRecord.evidence)} mr-2`}></i>
                      View Receipt
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
