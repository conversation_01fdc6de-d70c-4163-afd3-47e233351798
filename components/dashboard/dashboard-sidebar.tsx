"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { useUser } from "@/components/layout/user-context"
import { Button } from "@/components/ui/button"
import { useRealtimeUserStatus } from "@/hooks/use-user-status"
import { getAuthToken } from "@/utils/auth"
import { getUserProfile, UserProfileData } from "@/utils/users-api"

// 扩展用户类型以包含付费状态
type ExtendedUser = {
  id: string
  name: string
  email: string
  role: "user" | "admin"
  emailVerified?: boolean
  membership?: string
  organization?: string
  country?: string
  phone?: string
  position?: string
  paid?: boolean
}

// 导航项配置
const navigationItems = [
  {
    href: "/dashboard",
    icon: "fas fa-home",
    label: "Overview",
    requiresEmailVerified: false,
    requiresPaid: false,
  },
  {
    href: "/dashboard/payment",
    icon: "fas fa-credit-card",
    label: "Payment",
    requiresEmailVerified: true,
    requiresPaid: false,
  },
  {
    href: "/dashboard/submission",
    icon: "fas fa-file-alt",
    label: "Submission",
    requiresEmailVerified: true,
    requiresPaid: true,
  },
  {
    href: "/dashboard/accommodation",
    icon: "fas fa-hotel",
    label: "Accommodation",
    requiresEmailVerified: true,
    requiresPaid: true,
  },
  {
    href: "/profile",
    icon: "fas fa-user",
    label: "Profile",
    requiresEmailVerified: false,
    requiresPaid: false,
  },
]

export default function DashboardSidebar() {
  const pathname = usePathname()
  const { user: contextUser } = useUser()
  const { refresh, isLoading: realtimeLoading } = useRealtimeUserStatus()
  const [extendedUser, setExtendedUser] = useState<ExtendedUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // 获取完整的用户数据包括付费状态
  useEffect(() => {
    const fetchUserData = async () => {
      if (!contextUser?.id) {
        setIsLoading(false)
        return
      }

      try {
        const authToken = getAuthToken()
        if (!authToken) {
          setIsLoading(false)
          return
        }

        // 从 API 获取完整的用户数据
        const userProfileData: UserProfileData = await getUserProfile(contextUser.id)

        // 构建扩展的用户对象
        const roleValue = userProfileData.role || "user"
        const extended: ExtendedUser = {
          id: userProfileData.id.toString(),
          name: userProfileData.name,
          email: userProfileData.email,
          role: roleValue === "admin" || roleValue === "user" ? roleValue : "user",
          emailVerified: userProfileData.email_verified === true,
          membership: userProfileData.membership,
          organization: userProfileData.organization,
          country: userProfileData.country,
          phone: userProfileData.phone,
          position: userProfileData.position,
          paid: (userProfileData as UserProfileData & { paid?: boolean }).paid === true,
        }

        setExtendedUser(extended)
      } catch (error) {
        console.error("Error fetching user data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [contextUser?.id])

  if (isLoading) {
    return (
      <div className="sticky top-24 rounded-lg bg-white p-6 shadow-md">
        <div className="flex items-center justify-center py-8">
          <i className="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
        </div>
      </div>
    )
  }

  return (
    <div className="sticky top-24 rounded-lg bg-white p-6 shadow-md">
      <h2 className="mb-6 text-xl font-bold text-gray-800">Dashboard</h2>
      <nav className="space-y-2">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href
          const isAdmin = extendedUser?.role === "admin"

          // 检查是否需要邮箱验证
          const needsEmailVerification = item.requiresEmailVerified && !extendedUser?.emailVerified && !isAdmin
          // 检查是否需要付费（只有在邮箱已验证的情况下才检查付费状态）
          const needsPayment = item.requiresPaid && extendedUser?.emailVerified && !extendedUser?.paid && !isAdmin

          const isDisabled = needsEmailVerification || needsPayment

          if (isDisabled) {
            return (
              <div key={item.href} className="relative">
                <Button variant="ghost" className="w-full cursor-not-allowed justify-start opacity-50" disabled>
                  <i className={`${item.icon} mr-3 w-5 text-center`}></i>
                  {item.label}
                  <i className="fas fa-lock ml-auto text-xs"></i>
                </Button>
              </div>
            )
          }

          return (
            <Link key={item.href} href={item.href}>
              <Button variant={isActive ? "default" : "ghost"} className="w-full justify-start">
                <i className={`${item.icon} mr-3 w-5 text-center`}></i>
                {item.label}
              </Button>
            </Link>
          )
        })}
      </nav>

      {/* 帮助提示 */}
      {extendedUser && extendedUser.role !== "admin" && (
        <>
          {/* 邮箱验证提示 */}
          {!extendedUser.emailVerified && (
            <div className="mt-6 rounded-lg bg-orange-50 p-4">
              <div className="flex items-start">
                <i className="fas fa-envelope mt-0.5 mr-2 text-orange-500"></i>
                <div>
                  <p className="text-sm font-medium text-orange-900">Email Verification Required</p>
                  <p className="mt-1 text-xs text-orange-700">
                    Please verify your email address to access payment and other conference features.
                  </p>
                  <Link href="/profile" className="mt-2 inline-block">
                    <Button size="sm" className="text-xs">
                      Go to Profile
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          )}

          {/* 付费提示 */}
          {extendedUser.emailVerified && !extendedUser.paid && (
            <div className="mt-6 rounded-lg bg-blue-50 p-4">
              <div className="flex items-start">
                <i className="fas fa-info-circle mt-0.5 mr-2 text-blue-500"></i>
                <div>
                  <p className="text-sm font-medium text-blue-900">Payment Required</p>
                  <p className="mt-1 text-xs text-blue-700">
                    Complete your payment to unlock all conference features including abstract submission and
                    accommodation booking.
                  </p>
                  <div className="mt-2 flex gap-2">
                    <Link href="/dashboard/payment" className="inline-block">
                      <Button size="sm" className="text-xs">
                        Go to Payment
                      </Button>
                    </Link>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-xs"
                      onClick={refresh}
                      disabled={realtimeLoading}
                    >
                      <i className={`fas ${realtimeLoading ? "fa-spinner fa-spin" : "fa-sync-alt"} mr-1`}></i>
                      Refresh
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}
