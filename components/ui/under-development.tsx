"use client"

import { motion } from "framer-motion"
import { ReactNode } from "react"

interface UnderDevelopmentProps {
  children: ReactNode
  title?: string
  description?: string
  showOverlay?: boolean
}

/**
 * 毛玻璃效果的"开发中"覆盖组件
 * 用于显示正在开发中的功能
 */
export default function UnderDevelopment({
  children,
  title = "Under Development",
  description = "This feature is currently under development and will be available soon.",
  showOverlay = true,
}: UnderDevelopmentProps) {
  return (
    <div className="relative">
      {/* 原始内容 */}
      <div className={showOverlay ? "blur-sm" : ""}>{children}</div>

      {/* 毛玻璃覆盖层 */}
      {showOverlay && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="absolute inset-0 flex items-center justify-center"
          style={{
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(8px)",
            WebkitBackdropFilter: "blur(8px)",
          }}
        >
          <div className="max-w-md p-8 text-center">
            {/* 开发中图标 */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-6"
            >
              <div className="bg-muted mx-auto flex h-20 w-20 items-center justify-center rounded-full border shadow-lg">
                <i className="fas fa-tools text-muted-foreground text-2xl"></i>
              </div>
            </motion.div>

            {/* 标题 */}
            <motion.h3
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="text-foreground mb-3 text-xl font-bold"
            >
              {title}
            </motion.h3>

            {/* 描述 */}
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="text-muted-foreground text-sm leading-relaxed"
            >
              {description}
            </motion.p>

            {/* 加载动画 */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="mt-6 flex justify-center space-x-1"
            >
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="bg-muted-foreground h-2 w-2 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                />
              ))}
            </motion.div>

            {/* 预计完成时间 */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="text-muted-foreground mt-4 text-xs"
            >
              <i className="fas fa-clock mr-1"></i>
              Coming Soon
            </motion.div>
          </div>
        </motion.div>
      )}
    </div>
  )
}

/**
 * 简化版本的开发中组件，用于小型区域
 */
export function UnderDevelopmentSimple({
  title = "Under Development",
  className = "",
}: {
  title?: string
  className?: string
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`flex items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8 text-center ${className}`}
      style={{
        background: "rgba(249, 250, 251, 0.9)",
        backdropFilter: "blur(4px)",
        WebkitBackdropFilter: "blur(4px)",
      }}
    >
      <div>
        <div className="mb-3 flex justify-center">
          <div className="bg-muted flex h-12 w-12 items-center justify-center rounded-full border">
            <i className="fas fa-tools text-muted-foreground"></i>
          </div>
        </div>
        <h3 className="text-foreground mb-2 font-medium">{title}</h3>
        <p className="text-muted-foreground text-sm">This feature is coming soon</p>

        {/* 简单的加载动画 */}
        <div className="mt-3 flex justify-center space-x-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="bg-muted-foreground h-1.5 w-1.5 rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
              }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  )
}
