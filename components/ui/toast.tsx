"use client"

import { X } from "lucide-react"
import * as React from "react"
import { cn } from "@/lib/utils"

// Toast类型定义
export type ToastType = "success" | "error" | "warning" | "info"

export interface Toast {
  id: string
  type: ToastType
  title?: string
  message: string
  duration?: number
}

// Toast Context
interface ToastContextType {
  toasts: Toast[]
  addToast: (toast: Omit<Toast, "id">) => void
  removeToast: (id: string) => void
}

const ToastContext = React.createContext<ToastContextType | undefined>(undefined)

// Toast Provider
export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<Toast[]>([])

  const addToast = React.useCallback((toast: Omit<Toast, "id">) => {
    // 使用时间戳和计数器生成更稳定的ID
    const id = `toast-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    const newToast = { ...toast, id }

    setToasts((prev) => [...prev, newToast])

    // 自动移除toast
    const duration = toast.duration || 5000
    setTimeout(() => {
      removeToast(id)
    }, duration)
  }, [])

  const removeToast = React.useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }, [])

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  )
}

// Hook to use toast
export function useToast() {
  const context = React.useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

// Toast Container
function ToastContainer() {
  const { toasts } = useToast()

  return (
    <div className="fixed top-20 right-4 z-[9999] flex max-w-sm flex-col gap-2">
      {toasts.map((toast) => (
        <ToastItem key={toast.id} toast={toast} />
      ))}
    </div>
  )
}

// Individual Toast Item
function ToastItem({ toast }: { toast: Toast }) {
  const { removeToast } = useToast()

  const getToastStyles = (type: ToastType) => {
    switch (type) {
      case "success":
        return "bg-green-50 border-green-200 text-green-800"
      case "error":
        return "bg-red-50 border-red-200 text-red-800"
      case "warning":
        return "bg-yellow-50 border-yellow-200 text-yellow-800"
      case "info":
        return "bg-blue-50 border-blue-200 text-blue-800"
      default:
        return "bg-gray-50 border-gray-200 text-gray-800"
    }
  }

  const getIcon = (type: ToastType) => {
    switch (type) {
      case "success":
        return "✓"
      case "error":
        return "✕"
      case "warning":
        return "⚠"
      case "info":
        return "ℹ"
      default:
        return "ℹ"
    }
  }

  return (
    <div
      className={cn(
        "_duration-300 relative flex items-start gap-3 rounded-lg border p-4 shadow-lg transition-all ease-in-out",
        getToastStyles(toast.type)
      )}
    >
      <div className="flex-shrink-0 text-lg">{getIcon(toast.type)}</div>
      <div className="min-w-0 flex-1">
        {toast.title && <div className="mb-1 text-sm font-medium">{toast.title}</div>}
        <div className="text-sm">{toast.message}</div>
      </div>
      <button
        onClick={() => removeToast(toast.id)}
        className="ml-2 flex-shrink-0 text-gray-400 transition-colors hover:text-gray-600"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}

// 便捷的toast函数
export const toast = {
  success: (_message: string, _title?: string, _duration?: number) => {
    // 这个函数需要在组件内部使用useToast hook
  },
  error: (_message: string, _title?: string, _duration?: number) => {
    // 这个函数需要在组件内部使用useToast hook
  },
  warning: (_message: string, _title?: string, _duration?: number) => {
    // 这个函数需要在组件内部使用useToast hook
  },
  info: (_message: string, _title?: string, _duration?: number) => {
    // 这个函数需要在组件内部使用useToast hook
  },
}
