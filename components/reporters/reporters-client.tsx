"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { fadeIn } from "@/lib/animations"
// Types
type Reporter = {
  name: string
  title: string
  affiliation: string
  region: string
  field?: string
  photo?: string
}

type Statistics = {
  totalReporters: number
  countries: number
  institutions: number
  regions: {
    europe: number
    northAmerica: number
    china: number
    academic: number
    scientific: number
  }
  workplaceRegions: Record<string, Reporter[]>
}

type ReportersClientProps = {
  internationalReporters: Reporter[]
  chineseReporters: Reporter[]
  statistics: Statistics
}

export default function ReportersClient({
  internationalReporters,
  chineseReporters,
  statistics,
}: ReportersClientProps) {
  const [activeTab, setActiveTab] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredReporters, setFilteredReporters] = useState<Reporter[]>([])

  // Filter reporters based on search term and active tab
  useEffect(() => {
    // This function is defined inside useEffect to avoid recreating it on every render
    const getReportersByTab = () => {
      switch (activeTab) {
        case "international":
          return internationalReporters
        case "chinese":
          return chineseReporters
        default:
          return [...internationalReporters, ...chineseReporters] // Create allReporters inside the function
      }
    }

    const currentReporters = getReportersByTab()

    if (searchTerm.trim() === "") {
      setFilteredReporters(currentReporters)
    } else {
      const term = searchTerm.toLowerCase()
      setFilteredReporters(
        currentReporters.filter(
          (reporter) =>
            reporter.name.toLowerCase().includes(term) ||
            reporter.affiliation.toLowerCase().includes(term) ||
            reporter.title.toLowerCase().includes(term) ||
            (reporter.field && reporter.field.toLowerCase().includes(term))
        )
      )
    }
  }, [searchTerm, activeTab, internationalReporters, chineseReporters])

  return (
    <main className="pb-20">
      {/* Hero Section */}
      <section className="relative overflow-hidden pt-32 pb-16 text-white">
        <div className="absolute inset-0">
          <Image
            src="https://minioapi.bugmaker.me/ifmb-2025-public/home-page.jpg"
            alt="Conference Background"
            fill
            priority
            className="object-cover object-top"
          />
          <div className="absolute inset-0 bg-black/30 backdrop-blur-sm"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-blue-900/30"></div>
          <div
            className="absolute inset-0 opacity-10"
            style={{ backgroundImage: `url('https://minioapi.bugmaker.me/ifmb-2025-public/pattern.svg')` }}
          ></div>
        </div>
        <div className="relative z-10 container mx-auto px-6">
          <div className="mx-auto max-w-3xl p-8 text-center">
            <Badge className="mb-4 border-blue-400/50 bg-blue-600/40 px-3 py-1 text-white">IFMB 2025</Badge>
            <h1 className="mb-6 text-4xl font-bold md:text-5xl">Conference Speakers</h1>
            <p className="text-lg text-blue-100">
              Meet the distinguished scientists and experts who will be presenting at the International Forum on Maize
              Biology 2025
            </p>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="bg-white py-12">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-6xl">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <StatCard number={statistics.totalReporters} label="Total Speakers" icon="user-friends" color="green" />
              <StatCard
                number={statistics.regions.academic}
                label="Professors"
                icon="chalkboard-teacher"
                color="blue"
              />
              <StatCard number={statistics.regions.scientific} label="Scientists" icon="flask" color="amber" />
            </div>
          </div>
        </div>
      </section>

      {/* Reporters Section */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-6xl">
            <div className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Our Distinguished Speakers</h2>
              <p className="mx-auto max-w-3xl text-gray-600">
                Leading experts from around the world will share cutting-edge research and insights in maize biology.
              </p>
              <p className="mt-2 text-sm text-gray-500 italic">Sorted alphabetically by lastname (A-Z)</p>
            </div>

            {/* Search and Filter */}
            <div className="mb-10">
              <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
                <div className="w-full md:w-1/2">
                  <div className="relative">
                    <Input
                      type="text"
                      placeholder="Search by name, institution, title, or research field..."
                      className="border-gray-300 py-2 pl-10 focus:border-green-500 focus:ring-green-500"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute top-1/2 left-3 -translate-y-1/2 transform text-gray-400">
                      <i className="fas fa-search"></i>
                    </div>
                  </div>
                </div>
                <div className="w-full md:w-auto">
                  <div className="flex justify-center">
                    <div className="inline-flex rounded-lg bg-gray-100 p-1">
                      <button
                        className={`rounded-md px-4 py-2 text-sm font-medium ${
                          activeTab === "all" ? "bg-white text-gray-900 shadow-sm" : "text-gray-500 hover:text-gray-700"
                        }`}
                        onClick={() => setActiveTab("all")}
                      >
                        All ({internationalReporters.length + chineseReporters.length})
                      </button>
                      <button
                        className={`rounded-md px-4 py-2 text-sm font-medium ${
                          activeTab === "international"
                            ? "bg-white text-gray-900 shadow-sm"
                            : "text-gray-500 hover:text-gray-700"
                        }`}
                        onClick={() => setActiveTab("international")}
                      >
                        International ({internationalReporters.length})
                      </button>
                      <button
                        className={`rounded-md px-4 py-2 text-sm font-medium ${
                          activeTab === "chinese"
                            ? "bg-white text-gray-900 shadow-sm"
                            : "text-gray-500 hover:text-gray-700"
                        }`}
                        onClick={() => setActiveTab("chinese")}
                      >
                        Chinese ({chineseReporters.length})
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Results */}
            <motion.div
              key={activeTab + searchTerm}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="square-aspect-ratio-container"
            >
              {filteredReporters.length > 0 ? (
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
                  {filteredReporters.map((reporter, index) => (
                    <ReporterCard key={`${reporter.name}-${index}`} reporter={reporter} index={index} />
                  ))}
                </div>
              ) : (
                <div className="rounded-lg bg-white py-12 text-center shadow-sm">
                  <div className="mb-4 text-5xl text-gray-400">
                    <i className="fas fa-search"></i>
                  </div>
                  <h3 className="mb-2 text-xl font-semibold text-gray-700">No results found</h3>
                  <p className="text-gray-500">Try adjusting your search or filter to find what you're looking for.</p>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Regional Distribution by Workplace */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-6xl">
            <div className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Speaker Distribution by Workplace Region</h2>
              <p className="mx-auto max-w-3xl text-gray-600">
                Our speakers represent leading institutions from around the world, categorized by their workplace
                locations.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
              {Object.entries(statistics.workplaceRegions)
                .sort(([, a], [, b]) => b.length - a.length) // 按数量排序
                .map(([region, reporters]) => {
                  const count = reporters.length
                  const percentage = Math.round((count / statistics.totalReporters) * 100)

                  // 根据地区设置颜色和图标
                  let color = "gray"
                  let icon = "globe"

                  if (region === "China") {
                    color = "green"
                    icon = "map-marked-alt"
                  } else if (region === "United States") {
                    color = "amber"
                    icon = "flag-usa"
                  } else if (region === "Europe") {
                    color = "blue"
                    icon = "globe-europe"
                  } else if (region === "Other") {
                    color = "purple"
                    icon = "globe"
                  }

                  return (
                    <RegionCard
                      key={region}
                      region={region}
                      count={count}
                      percentage={percentage}
                      color={color}
                      icon={icon}
                    />
                  )
                })}
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="mb-6 text-3xl font-bold text-gray-900">Join Us at IFMB 2025</h2>
            <p className="mb-8 text-lg text-gray-600">
              Don't miss this opportunity to learn from and connect with these distinguished experts in maize biology.
            </p>
            <Link href="/register">
              <Button className="transform rounded-lg bg-green-700 px-8 py-4 text-lg font-semibold text-white shadow-md transition-all duration-300 hover:-translate-y-1 hover:bg-green-800">
                Register Now
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </main>
  )
}

// Reporter Card Component
type ReporterCardProps = {
  reporter: Reporter
  index: number
}

function ReporterCard({ reporter, index }: ReporterCardProps) {
  return (
    <motion.div
      initial="hidden"
      whileInView="show"
      viewport={{ once: true }}
      variants={fadeIn("up", 0.05 * ((index % 9) + 1))}
    >
      <Card className="flex h-full flex-col overflow-hidden p-6 transition-shadow hover:shadow-md">
        <div className="flex items-start space-x-4">
          {/* Photo area */}
          <div className="flex-shrink-0">
            {reporter.photo ? (
              <Image
                src={
                  reporter.photo.startsWith("http")
                    ? reporter.photo
                    : `https://minioapi.bugmaker.me/ifmb-2025-public/reporters/${reporter.photo}`
                }
                alt={`Photo of ${reporter.name}`}
                width={80}
                height={80}
                className="rounded-full object-cover"
              />
            ) : (
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gray-100 text-gray-400">
                <i className="fas fa-user text-2xl"></i>
              </div>
            )}
          </div>

          {/* Reporter info */}
          <div className="min-w-0 flex-1">
            <div className="flex flex-col space-y-1">
              <h4 className="text-lg font-bold break-words text-gray-900">{reporter.name}</h4>
              <div className="flex items-center justify-between">
                <p className="truncate text-sm text-green-600">{reporter.title}</p>
                <RegionBadge region={reporter.region} />
              </div>
              <p className="truncate text-sm text-gray-500">{reporter.affiliation}</p>
              <div className="mt-3 flex flex-wrap items-center gap-2">
                {reporter.field && (
                  <div title={reporter.field} className="inline-block">
                    <Badge className="cursor-help bg-blue-50 px-2 py-0.5 text-xs font-medium break-words whitespace-normal text-blue-700">
                      <i className="fas fa-microscope mr-1 flex-shrink-0"></i>
                      <span className="inline-block max-w-[150px] truncate">{reporter.field}</span>
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  )
}

// Region Badge Component
type RegionBadgeProps = {
  region: string
}

function RegionBadge({ region }: RegionBadgeProps) {
  let color: string

  switch (region) {
    case "Europe":
      color = "bg-blue-50 text-blue-700"
      break
    case "North America":
      color = "bg-amber-50 text-amber-700"
      break
    case "China":
    case "Asia":
      color = "bg-green-50 text-green-700"
      break
    case "Global":
      color = "bg-purple-50 text-purple-700"
      break
    default:
      color = "bg-gray-50 text-gray-700"
  }

  return <Badge className={`${color} px-2 py-0.5 text-xs font-medium`}>{region}</Badge>
}

// Stat Card Component
type StatCardProps = {
  number: number
  label: string
  icon: string
  color: string
}

function StatCard({ number, label, icon, color }: StatCardProps) {
  let colorClasses: string

  switch (color) {
    case "green":
      colorClasses = "bg-green-50 text-green-700"
      break
    case "blue":
      colorClasses = "bg-blue-50 text-blue-700"
      break
    case "amber":
      colorClasses = "bg-amber-50 text-amber-700"
      break
    case "purple":
      colorClasses = "bg-purple-50 text-purple-700"
      break
    default:
      colorClasses = "bg-gray-50 text-gray-700"
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <Card className="p-6 text-center transition-shadow hover:shadow-md">
        <div className={`h-12 w-12 rounded-full ${colorClasses} mx-auto mb-4 flex items-center justify-center`}>
          <i className={`fas fa-${icon} text-xl`}></i>
        </div>
        <div className="mb-1 text-3xl font-bold text-gray-900">{number}</div>
        <div className="text-sm text-gray-500">{label}</div>
      </Card>
    </motion.div>
  )
}

// Region Card Component
type RegionCardProps = {
  region: string
  count: number
  percentage: number
  color: string
  icon: string
}

function RegionCard({ region, count, percentage, color, icon }: RegionCardProps) {
  let colorClasses: string
  let bgColor: string

  switch (color) {
    case "green":
      colorClasses = "from-green-600/80 to-green-800/90"
      bgColor = "bg-green-700/80"
      break
    case "blue":
      colorClasses = "from-blue-600/80 to-blue-800/90"
      bgColor = "bg-blue-700/80"
      break
    case "amber":
      colorClasses = "from-amber-600/80 to-amber-800/90"
      bgColor = "bg-amber-700/80"
      break
    case "purple":
      colorClasses = "from-purple-600/80 to-purple-800/90"
      bgColor = "bg-purple-700/80"
      break
    default:
      colorClasses = "from-gray-600/80 to-gray-800/90"
      bgColor = "bg-gray-700/80"
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <Card className="h-full overflow-hidden">
        <div className={`h-40 bg-gradient-to-br ${colorClasses} relative flex items-center justify-center text-white`}>
          <div className="absolute top-0 left-0 h-full w-full opacity-10">
            <div className="absolute top-2 left-2 text-6xl text-white/20">
              <i className={`fas fa-${icon}`}></i>
            </div>
            <div className="absolute right-2 bottom-2 text-6xl text-white/20">
              <i className={`fas fa-${icon}`}></i>
            </div>
          </div>
          <div className="z-10 text-center">
            <h3 className="mb-1 text-xl font-bold">{region}</h3>
            <div className="text-3xl font-bold">{count}</div>
            <div className="text-sm opacity-90">Reporters</div>
          </div>
        </div>
        <div className="bg-white p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">Percentage</div>
            <div className="text-sm font-medium text-gray-900">{percentage}%</div>
          </div>
          <div className="mt-2 h-2 overflow-hidden rounded-full bg-gray-200">
            <div className={`h-full ${bgColor} rounded-full`} style={{ width: `${percentage}%` }}></div>
          </div>
        </div>
      </Card>
    </motion.div>
  )
}
