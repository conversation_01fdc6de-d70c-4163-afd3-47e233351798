"use client"

import { motion } from "framer-motion"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { fadeIn } from "@/lib/animations"

type Speaker = {
  name: string
  title: string
  institution: string
  image?: string
  topic?: string
}

type SpeakerCardProps = {
  speaker: Speaker
  index: number
}

export default function SpeakerCard({ speaker, index }: SpeakerCardProps) {
  return (
    <motion.div
      initial="hidden"
      whileInView="show"
      viewport={{ once: true, amount: 0.25 }}
      variants={fadeIn("up", 0.2 * (index + 1))}
      className="group"
    >
      <div className="transform rounded-xl bg-white p-6 text-center shadow-md transition-all duration-300 group-hover:-translate-y-2 hover:shadow-xl">
        <div className="relative mx-auto mb-6">
          <Avatar className="mx-auto h-32 w-32 border-4 border-green-100 transition-all duration-300 group-hover:border-green-200">
            {speaker.image && <AvatarImage src={speaker.image} alt={speaker.name} className="object-cover" />}
            <AvatarFallback>
              {speaker.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div className="absolute -bottom-3 left-1/2 -translate-x-1/2 transform rounded-full bg-green-600 px-3 py-1 text-xs text-white shadow-md">
            Keynote
          </div>
        </div>

        <h3 className="mb-1 text-xl font-bold text-gray-800">{speaker.name}</h3>
        <p className="mb-2 font-medium text-green-600">{speaker.institution}</p>
        <p className="mb-4 text-sm text-gray-600">{speaker.title}</p>

        <div className="border-t border-gray-100 pt-4">
          <p className="text-sm font-medium text-gray-700">
            <span className="text-gray-500">Topic:</span> {speaker.topic}
          </p>
        </div>
      </div>
    </motion.div>
  )
}
