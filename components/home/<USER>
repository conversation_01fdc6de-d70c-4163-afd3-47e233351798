"use client"

import { motion } from "framer-motion"
import { Card } from "@/components/ui/card"
import { fadeIn } from "@/lib/animations"

type ImportantDate = {
  title: string
  date: string
  icon: string
  color: string
  description: string
}

type DateCardProps = {
  date: ImportantDate
  index: number
}

export default function DateCard({ date, index }: DateCardProps) {
  const getColorClasses = (color: string) => {
    switch (color) {
      case "blue":
        return {
          border: "border-amber-500",
          bg: "bg-amber-100",
          text: "text-amber-600",
        }
      case "green":
        return {
          border: "border-amber-500",
          bg: "bg-amber-100",
          text: "text-amber-600",
        }
      case "yellow":
        return {
          border: "border-yellow-500",
          bg: "bg-yellow-100",
          text: "text-yellow-600",
        }
      case "amber":
        return {
          border: "border-amber-500",
          bg: "bg-amber-100",
          text: "text-amber-600",
        }
      case "purple":
        return {
          border: "border-amber-500",
          bg: "bg-amber-100",
          text: "text-amber-600",
        }
      default:
        return {
          border: "border-gray-500",
          bg: "bg-gray-100",
          text: "text-gray-600",
        }
    }
  }

  const colorClasses = getColorClasses(date.color)

  return (
    <motion.div
      initial="hidden"
      whileInView="show"
      viewport={{ once: true, amount: 0.25 }}
      variants={fadeIn("up", 0.2 * (index + 1))}
    >
      <Card
        className={`border-l-4 ${colorClasses.border} square:h-[200px] square:p-4 flex h-[280px] transform flex-col p-6 transition-all transition-shadow duration-300 hover:-translate-y-1 hover:shadow-xl`}
      >
        <div className="square:space-x-3 flex h-full items-start space-x-4">
          <div
            className={`rounded-full ${colorClasses.bg} p-3 ${colorClasses.text} square:h-10 square:w-10 square:p-2 flex h-12 w-12 flex-shrink-0 items-center justify-center`}
          >
            <i className={`fas fa-${date.icon} square:text-lg text-xl`}></i>
          </div>
          <div className="square:justify-between flex flex-grow flex-col">
            <div>
              <h3 className="square:h-10 square:text-sm square:mb-3 mb-2 flex h-18 items-start text-lg font-semibold text-gray-800">
                {date.title}
              </h3>
              <p className="square:mb-3 square:h-5 square:text-xs mb-2 h-10 font-medium text-gray-600">{date.date}</p>
            </div>
            <div className="square:mt-3 mt-auto">
              <p className="square:line-clamp-2 square:text-xs line-clamp-3 text-sm text-gray-500">
                {date.description}
              </p>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  )
}
