"use client"

import { motion } from "framer-motion"
import { useEffect, useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card } from "@/components/ui/card"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { fadeIn } from "@/lib/animations"

// Types
type Speaker = {
  name: string
  title: string
  institution: string
  image?: string
  topic?: string
}

type KeySpeakersCarouselProps = {
  speakers: Speaker[]
}

export default function KeySpeakersCarousel({ speakers }: KeySpeakersCarouselProps) {
  const [isMounted, setIsMounted] = useState(false)

  // Ensure component is mounted before rendering carousel (for SSR compatibility)
  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return null
  }

  return (
    <div className="relative">
      <Carousel
        opts={{
          align: "start",
          loop: true,
        }}
        className="w-full"
      >
        <CarouselContent className="-ml-2 md:-ml-4">
          {speakers.map((speaker, index) => (
            <CarouselItem key={speaker.name} className="h-full pl-2 md:basis-1/2 md:pl-4 lg:basis-1/3 xl:basis-1/4">
              <div className="h-full p-1">
                <SpeakerCard speaker={speaker} index={index} />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className="mt-8 flex items-center justify-center">
          <CarouselPrevious className="static mr-4 translate-y-0" />
          <CarouselNext className="static ml-4 translate-y-0" />
        </div>
      </Carousel>
    </div>
  )
}

// Speaker Card Component
function SpeakerCard({ speaker, index }: { speaker: Speaker; index: number }) {
  return (
    <motion.div
      initial="hidden"
      whileInView="show"
      viewport={{ once: true }}
      variants={fadeIn("up", 0.1 * ((index % 4) + 1))}
      className="h-full"
    >
      <Card className="flex h-[280px] transform flex-col rounded-xl bg-white p-6 text-center shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-xl">
        <div className="relative mx-auto mb-6 h-32">
          <Avatar className="mx-auto h-24 w-24 border-4 border-green-100 transition-all duration-300 group-hover:border-green-200">
            {speaker.image && (
              <AvatarImage
                src={
                  speaker.image.startsWith("http")
                    ? speaker.image
                    : `https://minioapi.bugmaker.me/ifmb-2025-public/reporters/${speaker.image}`
                }
                alt={speaker.name}
                className="object-cover"
              />
            )}
            <AvatarFallback>
              {speaker.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div className="absolute -bottom-3 left-1/2 -translate-x-1/2 transform rounded-full bg-green-600 px-3 py-1 text-xs text-white shadow-md">
            Keynote
          </div>
        </div>

        <div className="flex h-24 flex-grow flex-col">
          <h3 className="mb-1 text-lg font-bold text-gray-800">{speaker.name}</h3>
          <p className="mb-1 text-sm font-medium text-green-600">{speaker.title}</p>
          <p className="mb-3 line-clamp-2 h-8 overflow-hidden text-xs text-gray-600">{speaker.institution}</p>
        </div>

        {speaker.topic && (
          <div className="mt-auto w-full border-t border-gray-100 pt-2">
            <p className="line-clamp-2 h-8 overflow-hidden text-xs font-medium text-gray-700">
              <span className="text-gray-500">Topic:</span> {speaker.topic}
            </p>
          </div>
        )}
      </Card>
    </motion.div>
  )
}
