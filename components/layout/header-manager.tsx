"use client"

import { usePathname } from "next/navigation"
import * as React from "react"
import { useEffect, useRef, useState } from "react"
import { createPortal } from "react-dom"

import { Header } from "./header"

// 全局header容器管理器 - 使用单例模式
class GlobalHeaderContainer {
  private static instance: GlobalHeaderContainer
  private container: HTMLElement | null = null
  private isInitialized = false

  private constructor() {}

  public static getInstance(): GlobalHeaderContainer {
    if (!GlobalHeaderContainer.instance) {
      GlobalHeaderContainer.instance = new GlobalHeaderContainer()
    }
    return GlobalHeaderContainer.instance
  }

  public getContainer(): HTMLElement | null {
    if (typeof window === "undefined") return null

    if (!this.container && !this.isInitialized) {
      this.container = document.createElement("div")
      this.container.id = "global-header-root"
      this.container.style.position = "relative"
      this.container.style.zIndex = "50"
      document.body.appendChild(this.container)
      this.isInitialized = true
    }

    return this.container
  }

  public cleanup() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
      this.container = null
      this.isInitialized = false
    }
  }
}

// Header状态管理器 - 监听路径变化并计算header状态
class HeaderStateManager {
  private static instance: HeaderStateManager
  private listeners: Set<(state: HeaderState) => void> = new Set()
  private currentState: HeaderState = {
    isScrolled: false,
    needsSolidHeader: false,
    shouldShow: true,
  }

  private constructor() {
    if (typeof window !== "undefined") {
      this.initializeScrollListener()
    }
  }

  public static getInstance(): HeaderStateManager {
    if (!HeaderStateManager.instance) {
      HeaderStateManager.instance = new HeaderStateManager()
    }
    return HeaderStateManager.instance
  }

  private initializeScrollListener() {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10
      if (isScrolled !== this.currentState.isScrolled) {
        this.updateState({ isScrolled })
      }
    }

    window.addEventListener("scroll", handleScroll, { passive: true })

    // 清理函数（虽然在这个场景下不会被调用）
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }

  public updatePath(pathname: string) {
    const isAdminPage = pathname?.startsWith("/admin")
    const isAuthPage = ["/register", "/login", "/forgot-password"].includes(pathname)
    const needsSolidHeader = isAuthPage || pathname?.startsWith("/profile") || pathname?.startsWith("/dashboard")
    const shouldShow = !isAdminPage

    this.updateState({
      needsSolidHeader,
      shouldShow,
    })
  }

  private updateState(newState: Partial<HeaderState>) {
    const hasChanged = Object.keys(newState).some(
      (key) => this.currentState[key as keyof HeaderState] !== newState[key as keyof HeaderState]
    )

    if (hasChanged) {
      this.currentState = { ...this.currentState, ...newState }
      this.notifyListeners()
    }
  }

  public subscribe(listener: (state: HeaderState) => void): () => void {
    this.listeners.add(listener)
    // 立即调用一次以获取当前状态
    listener(this.currentState)

    return () => {
      this.listeners.delete(listener)
    }
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.currentState))
  }

  public getCurrentState(): HeaderState {
    return this.currentState
  }
}

interface HeaderState {
  isScrolled: boolean
  needsSolidHeader: boolean
  shouldShow: boolean
}

// 稳定的Header渲染器 - 只渲染一次，通过状态管理器更新
const StableHeaderRenderer = React.memo(function StableHeaderRenderer() {
  const [headerState, setHeaderState] = useState<HeaderState>({
    isScrolled: false,
    needsSolidHeader: false,
    shouldShow: true,
  })

  useEffect(() => {
    const stateManager = HeaderStateManager.getInstance()
    const unsubscribe = stateManager.subscribe(setHeaderState)

    return unsubscribe
  }, [])

  if (!headerState.shouldShow) {
    return null
  }

  return <Header isScrolled={headerState.isScrolled} needsSolidHeader={headerState.needsSolidHeader} />
})

// 路径监听器 - 只负责监听路径变化并更新状态管理器
const PathListener = React.memo(function PathListener() {
  const pathname = usePathname()
  const stateManager = HeaderStateManager.getInstance()

  useEffect(() => {
    stateManager.updatePath(pathname)
  }, [pathname, stateManager])

  return null
})

// 主Header管理器组件
export const HeaderManager = React.memo(function HeaderManager() {
  const [isClient, setIsClient] = useState(false)
  const isInitialized = useRef(false)
  const containerInstance = GlobalHeaderContainer.getInstance()

  useEffect(() => {
    setIsClient(true)

    if (!isInitialized.current) {
      isInitialized.current = true
    }
  }, [])

  const headerContainer = containerInstance.getContainer()

  if (!isClient || !headerContainer) {
    return <PathListener />
  }

  return (
    <>
      <PathListener />
      {createPortal(<StableHeaderRenderer />, headerContainer)}
    </>
  )
})

export default HeaderManager
