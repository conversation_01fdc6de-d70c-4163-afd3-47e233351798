"use client"

import React, { create<PERSON>ontext, ReactNode, useCallback, useContext, useEffect, useMemo, useState } from "react"

// 用户类型定义
type User = {
  id: string
  username?: string
  name: string
  email: string
  avatar?: string
  role: "user" | "admin"
  email_verified?: boolean
  membership?: string
  organization?: string
  country?: string
  phone?: string
  position?: string
  paid?: boolean
}

// Context 类型定义
type UserContextType = {
  user: User | null
  setUser: (user: User | null) => void
  logout: () => void
}

// 创建 Context
const UserContext = createContext<UserContextType | undefined>(undefined)

// Provider 组件
export const UserProvider = React.memo(function UserProvider({ children }: { children: ReactNode }) {
  // 安全的初始化，确保在服务器端不访问 localStorage
  const [user, setUser] = useState<User | null>(() => {
    // 确保只在客户端执行
    if (typeof window === "undefined") return null

    try {
      const userData = localStorage.getItem("user")
      if (userData) {
        try {
          let parsedData
          try {
            parsedData = JSON.parse(userData) as unknown
          } catch (_jsonError) {
            localStorage.removeItem("user")
            return null
          }

          const parsedDataTyped = parsedData as Record<string, unknown>
          const userInfo =
            parsedDataTyped.user_info ||
            ((parsedDataTyped.data as Record<string, unknown>) &&
              (parsedDataTyped.data as Record<string, unknown>).user_info)

          if (userInfo && typeof userInfo === "object" && "id" in userInfo) {
            // 直接使用后端返回的数据，不需要额外处理
            const normalizedUser = {
              ...userInfo,
            }
            return normalizedUser as User
          } else {
            return null
          }
        } catch (_error) {
          return null
        }
      }
      return null
    } catch (_error) {
      return null
    }
  })

  // 监听 localStorage 变化（用于跨标签页同步）
  useEffect(() => {
    // 确保只在客户端执行
    if (typeof window === "undefined") return

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "user") {
        if (e.newValue) {
          try {
            const parsedData = JSON.parse(e.newValue) as unknown
            const parsedDataTyped = parsedData as Record<string, unknown>
            const userInfo =
              parsedDataTyped.user_info ||
              ((parsedDataTyped.data as Record<string, unknown>) &&
                (parsedDataTyped.data as Record<string, unknown>).user_info)
            if (userInfo && typeof userInfo === "object" && "id" in userInfo) {
              // 直接使用后端返回的数据，不需要额外处理
              const normalizedUser = {
                ...userInfo,
              }
              setUser(normalizedUser as User)
            }
          } catch (_error) {
            setUser(null)
          }
        } else {
          setUser(null)
        }
      }
    }

    window.addEventListener("storage", handleStorageChange)
    return () => window.removeEventListener("storage", handleStorageChange)
  }, [])

  // 登出功能 - 使用 useCallback 避免每次重新创建
  const logout = useCallback(() => {
    if (typeof window !== "undefined") {
      try {
        localStorage.removeItem("user")
      } catch (_error) {
        // 忽略错误
      }
    }
    setUser(null)
  }, [setUser])

  // 使用 useMemo 来避免 value 对象每次重新创建
  const value = useMemo(
    () => ({
      user,
      setUser,
      logout,
    }),
    [user, setUser, logout]
  )

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>
})

// Hook 来使用 Context
export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}
