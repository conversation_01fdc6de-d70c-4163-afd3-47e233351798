"use client"

import { <PERSON><PERSON>ronDown, ChevronUp, LogOut, <PERSON>u, <PERSON><PERSON><PERSON>, User, X } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import * as React from "react"
import { useState } from "react"

import { useUser } from "@/components/layout/user-context"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { CircularLogo } from "@/components/ui/logo"

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { cn } from "@/lib/utils"

// Navigation items configuration
const navigationItems = [
  {
    title: "Conference",
    items: [
      {
        title: "Organization",
        href: "/organization",
        description: "Learn about the organizing committee and conference structure",
      },
      {
        title: "Executive Committee",
        href: "/executive",
        description: "Meet the executive committee members",
      },
      {
        title: "Reporters",
        href: "/reporters",
        description: "Distinguished speakers and presenters",
      },
      {
        title: "Schedule",
        href: "/schedule",
        description: "General conference program and daily schedule",
      },
    ],
  },
  {
    title: "Participation",
    items: [
      {
        title: "Registration",
        href: "/registration",
        description: "Register for the conference and submit your information",
      },
      {
        title: "Abstract Submission",
        href: "/abstract-submission",
        description: "Submit your research abstract and learn about presentation guidelines",
      },
      {
        title: "Transportation",
        href: "/transportation",
        description: "Travel information and accommodation details",
      },
      {
        title: "Sponsorship",
        href: "/sponsorship",
        description: "Sponsorship opportunities and exhibition information",
      },
    ],
  },
]

const ListItem = React.forwardRef<React.ElementRef<"a">, React.ComponentPropsWithoutRef<"a">>(
  ({ className, title, children, ...props }, ref) => {
    return (
      <li>
        <NavigationMenuLink asChild>
          <a
            ref={ref}
            className={cn(
              "hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground block space-y-1 rounded-md p-3 leading-none no-underline transition-colors outline-none select-none",
              className
            )}
            {...props}
          >
            <div className="text-sm leading-none font-medium">{title}</div>
            <p className="text-muted-foreground line-clamp-2 text-sm leading-snug">{children}</p>
          </a>
        </NavigationMenuLink>
      </li>
    )
  }
)
ListItem.displayName = "ListItem"

// Props interface for header styling
interface StableHeaderProps {
  isScrolled?: boolean
  needsSolidHeader?: boolean
}

// 稳定的Header组件 - 不依赖usePathname
export const Header = React.memo(function StableHeader({
  isScrolled = false,
  needsSolidHeader = false,
}: StableHeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [expandedMobileSection, setExpandedMobileSection] = useState<string | null>(null)
  const router = useRouter()
  const { user, logout } = useUser()

  // 登出处理 - 使用useCallback优化
  const handleLogout = React.useCallback(() => {
    logout()
    router.push("/")
  }, [logout, router])

  // 获取用户名首字母 - 使用useCallback优化
  const getUserInitials = React.useCallback((name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }, [])

  // 用户首字母 - 使用useMemo缓存
  const userInitials = React.useMemo(() => {
    return user ? getUserInitials(user.name) : ""
  }, [user, getUserInitials])

  return (
    <header
      className={cn(
        "fixed top-0 right-0 left-0 z-50 transition-all duration-300",
        isScrolled || needsSolidHeader
          ? "border-b border-yellow-200 bg-white/98 shadow-sm backdrop-blur supports-[backdrop-filter]:bg-white/95"
          : "border-b border-white/20 bg-white/30 backdrop-blur-md"
      )}
    >
      <div className="container mx-auto flex h-16 items-center justify-between px-6">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-3">
          <CircularLogo size={40} />
          <div className="hidden sm:block">
            <h1 className="text-lg font-bold tracking-tight">
              <span className="hidden text-gray-800 drop-shadow-sm lg:inline">International Forum on </span>
              <span className="text-green-700 drop-shadow-sm">Maize Biology</span>
            </h1>
          </div>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden items-center space-x-6 lg:flex">
          <NavigationMenu>
            <NavigationMenuList>
              {/* Overview as standalone item */}
              <NavigationMenuItem>
                <NavigationMenuLink asChild>
                  <Link
                    href="/"
                    className={cn(
                      navigationMenuTriggerStyle(),
                      "bg-transparent font-medium text-gray-800 drop-shadow-sm hover:text-green-600"
                    )}
                  >
                    Overview
                  </Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              {/* Other dropdown items */}
              {navigationItems.map((item) => (
                <NavigationMenuItem key={item.title}>
                  <NavigationMenuTrigger className="bg-transparent font-medium text-gray-800 drop-shadow-sm hover:text-green-600 data-[state=open]:text-green-600">
                    {item.title}
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      {item.items.map((subItem) => (
                        <ListItem key={subItem.title} title={subItem.title} href={subItem.href}>
                          {subItem.description}
                        </ListItem>
                      ))}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>

          {/* User Menu */}
          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-10 w-10 rounded-full p-0">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full border-2 border-white bg-gradient-to-br from-green-500 to-green-600 text-sm font-bold text-white shadow-lg transition-all duration-200 hover:scale-105 hover:shadow-xl">
                    {userInitials}
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="flex flex-col space-y-1 leading-none">
                    <p className="font-medium">{user.name}</p>
                    <p className="text-muted-foreground w-[200px] truncate text-sm">{user.email}</p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard">
                    <Settings className="mr-2 h-4 w-4" />
                    Dashboard
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/profile">
                    <User className="mr-2 h-4 w-4" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                {user.role === "admin" && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/admin" className="text-orange-600 hover:text-orange-700">
                        <Settings className="mr-2 h-4 w-4" />
                        Admin Panel
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                asChild
                className="font-medium text-gray-800 drop-shadow-sm hover:bg-green-50 hover:text-green-600"
              >
                <Link href="/login">Login</Link>
              </Button>
              <Button
                asChild
                className="bg-gradient-to-r from-yellow-500 to-yellow-600 font-medium text-white shadow-md hover:from-yellow-600 hover:to-yellow-700"
              >
                <Link href="/register">Register</Link>
              </Button>
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          className="lg:hidden"
          size="icon"
          onClick={() => {
            setIsMobileMenuOpen(!isMobileMenuOpen)
            if (isMobileMenuOpen) {
              setExpandedMobileSection(null)
            }
          }}
        >
          {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </Button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="border-t border-yellow-200 bg-white/98 backdrop-blur supports-[backdrop-filter]:bg-white/95 lg:hidden">
          <div className="container mx-auto px-6 py-4">
            <nav className="flex flex-col space-y-4">
              {/* Overview as standalone item */}
              <Link
                href="/"
                className="block py-2 text-sm font-medium text-gray-800 transition-colors hover:text-green-600"
                onClick={() => {
                  setIsMobileMenuOpen(false)
                  setExpandedMobileSection(null)
                }}
              >
                Overview
              </Link>

              {/* Other dropdown sections */}
              {navigationItems.map((item) => (
                <div key={item.title} className="space-y-2">
                  <button
                    className="text-muted-foreground flex w-full items-center justify-between text-left text-sm font-medium transition-colors hover:text-green-600"
                    onClick={() => setExpandedMobileSection(expandedMobileSection === item.title ? null : item.title)}
                  >
                    {item.title}
                    {expandedMobileSection === item.title ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </button>
                  {expandedMobileSection === item.title && (
                    <div className="ml-4 space-y-2">
                      {item.items.map((subItem) => (
                        <Link
                          key={subItem.title}
                          href={subItem.href}
                          className="block text-sm font-medium text-gray-800 transition-colors hover:text-green-600"
                          onClick={() => {
                            setIsMobileMenuOpen(false)
                            setExpandedMobileSection(null)
                          }}
                        >
                          {subItem.title}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}

              {/* Mobile User Menu */}
              <div className="border-t pt-4">
                {user ? (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 p-2">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full border-2 border-white bg-gradient-to-br from-green-500 to-green-600 text-sm font-bold text-white shadow-md">
                        {userInitials}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-800">{user.name}</p>
                        <p className="text-xs text-gray-600">{user.email}</p>
                      </div>
                    </div>
                    <Link
                      href="/dashboard"
                      className="block p-2 text-sm font-medium text-gray-800 transition-colors hover:text-green-600"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      href="/profile"
                      className="block p-2 text-sm font-medium text-gray-800 transition-colors hover:text-green-600"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Profile
                    </Link>
                    {user.role === "admin" && (
                      <Link
                        href="/admin"
                        className="block p-2 text-sm text-orange-600 transition-colors hover:text-orange-700"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Admin Panel
                      </Link>
                    )}
                    <button
                      onClick={() => {
                        handleLogout()
                        setIsMobileMenuOpen(false)
                      }}
                      className="block w-full p-2 text-left text-sm font-medium text-gray-800 transition-colors hover:text-green-600"
                    >
                      Log out
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Button
                      variant="ghost"
                      asChild
                      className="w-full justify-start font-medium text-gray-800 hover:bg-green-50 hover:text-green-600"
                    >
                      <Link href="/login" onClick={() => setIsMobileMenuOpen(false)}>
                        Login
                      </Link>
                    </Button>
                    <Button
                      asChild
                      className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 font-medium text-white hover:from-yellow-600 hover:to-yellow-700"
                    >
                      <Link href="/register" onClick={() => setIsMobileMenuOpen(false)}>
                        Register
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            </nav>
          </div>
        </div>
      )}
    </header>
  )
})
