"use client"

import React from "react"

// 页面加载占位符组件
export default function PageLoading() {
  return (
    <main className="pt-20 pb-20">
      {/* Hero Section Placeholder */}
      <section className="relative overflow-hidden bg-gradient-to-b from-blue-900/20 to-transparent pt-16 pb-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-4xl text-center">
            {/* Badge placeholder */}
            <div className="mx-auto mb-4 h-6 w-24 animate-pulse rounded-full bg-gray-200"></div>

            {/* Title placeholder */}
            <div className="mb-6 space-y-3">
              <div className="mx-auto h-12 w-96 animate-pulse rounded bg-gray-200"></div>
              <div className="mx-auto h-12 w-80 animate-pulse rounded bg-gray-200"></div>
            </div>

            {/* Description placeholder */}
            <div className="mb-8 space-y-2">
              <div className="mx-auto h-4 w-full max-w-2xl animate-pulse rounded bg-gray-200"></div>
              <div className="mx-auto h-4 w-full max-w-xl animate-pulse rounded bg-gray-200"></div>
            </div>

            {/* Buttons placeholder */}
            <div className="mb-12 flex flex-col justify-center gap-4 sm:flex-row">
              <div className="h-12 w-40 animate-pulse rounded-lg bg-gray-200"></div>
              <div className="h-12 w-40 animate-pulse rounded-lg bg-gray-200"></div>
            </div>

            {/* Stats cards placeholder */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="rounded-lg bg-white/10 p-4 backdrop-blur-sm">
                  <div className="mx-auto mb-2 h-8 w-16 animate-pulse rounded bg-gray-200"></div>
                  <div className="mx-auto h-4 w-24 animate-pulse rounded bg-gray-200"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Content Section Placeholder */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-4xl">
            {/* Section title placeholder */}
            <div className="mb-12 text-center">
              <div className="mx-auto mb-4 h-8 w-64 animate-pulse rounded bg-gray-200"></div>
              <div className="mx-auto mb-2 h-4 w-full max-w-2xl animate-pulse rounded bg-gray-200"></div>
              <div className="mx-auto h-4 w-full max-w-xl animate-pulse rounded bg-gray-200"></div>
            </div>

            {/* Cards placeholder */}
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="rounded-lg border border-gray-200 p-6">
                  <div className="mb-4 h-6 w-full animate-pulse rounded bg-gray-200"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-full animate-pulse rounded bg-gray-200"></div>
                    <div className="h-4 w-3/4 animate-pulse rounded bg-gray-200"></div>
                    <div className="h-4 w-1/2 animate-pulse rounded bg-gray-200"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Additional sections placeholder */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-6xl">
            {/* Section title */}
            <div className="mb-12 text-center">
              <div className="mx-auto mb-4 h-8 w-48 animate-pulse rounded bg-gray-200"></div>
              <div className="mx-auto h-4 w-full max-w-lg animate-pulse rounded bg-gray-200"></div>
            </div>

            {/* Grid placeholder */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="rounded-lg bg-white p-6 shadow-sm">
                  <div className="mb-4 h-32 w-full animate-pulse rounded bg-gray-200"></div>
                  <div className="mb-2 h-6 w-3/4 animate-pulse rounded bg-gray-200"></div>
                  <div className="mb-1 h-4 w-full animate-pulse rounded bg-gray-200"></div>
                  <div className="h-4 w-2/3 animate-pulse rounded bg-gray-200"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
