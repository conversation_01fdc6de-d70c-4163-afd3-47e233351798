"use client"

import React, { useEffect, useState } from "react"
import RouteGuard from "@/components/auth/route-guard"
import Footer from "@/components/layout/footer"
import { HeaderManager } from "@/components/layout/header-manager"
import { UserProvider } from "@/components/layout/user-context"
import { ToastProvider } from "@/components/ui/toast"

// 客户端根组件 - 包含用户上下文、header管理器和footer
const ClientRoot = React.memo(function ClientRoot({ children }: { children: React.ReactNode }) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  return (
    <ToastProvider>
      <UserProvider>
        {isClient && <HeaderManager />}
        <RouteGuard>
          <main>{children}</main>
        </RouteGuard>
        <Footer />
      </UserProvider>
    </ToastProvider>
  )
})

export default ClientRoot
