# 本地开发环境配置示例
# 复制此文件为 .env.local 并根据需要修改配置

# API配置 - 本地开发
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001

# 高德地图API配置 (可选，用于地图功能)
AMAP_API_KEY=your_amap_api_key_here
AMAP_SECRET=your_amap_secret_here
NEXT_PUBLIC_AMAP_API_KEY=your_amap_api_key_here

# Cloudflare Turnstile配置 (可选，开发环境会自动绕过)
# 如果不配置，开发环境会使用假token绕过验证
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key_here
TURNSTILE_SECRET_KEY=your_turnstile_secret_key_here

# 开发环境说明：
# 1. 在开发环境中，Turnstile验证会被自动绕过
# 2. 如果没有配置TURNSTILE keys，系统会使用开发模式
# 3. API_BASE_URL 可以指向本地后端服务器
