/**
 * 密码加密工具
 * 提供一致的密码加密方法，用于注册、登录和找回密码等功能
 */

/**
 * 使用固定的盐值对密码进行加密
 * 这确保了相同的密码在前端加密后始终得到相同的哈希值
 * 注意：这是前端的第一层加密，后端会进行二次加密存储
 *
 * @param password 原始密码
 * @returns 加密后的密码
 */
export const encryptPassword = async (password: string): Promise<string> => {
  // 使用固定的盐值，确保相同密码加密结果一致
  // 在实际生产环境中，这个盐值应该是一个环境变量或配置项
  const FIXED_SALT = "IFMB2025_FIXED_SALT"

  // 使用 TextEncoder 将字符串转换为 Uint8Array
  const encoder = new TextEncoder()
  const data = encoder.encode(password + FIXED_SALT)

  // 使用 SubtleCrypto API 的 SHA-256 算法进行哈希
  const hashBuffer = await crypto.subtle.digest("SHA-256", data)

  // 将 ArrayBuffer 转换为十六进制字符串
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, "0")).join("")

  return hashHex
}
