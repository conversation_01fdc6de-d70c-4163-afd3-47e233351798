import { NextRequest } from "next/server"

/**
 * 服务器端认证工具函数
 */

/**
 * 验证JWT token的辅助函数
 * @param request NextRequest 对象
 * @returns 验证结果对象
 */
export function verifyAuthToken(request: NextRequest): { isValid: boolean; userId?: string; error?: string } {
  try {
    const authHeader = request.headers.get("authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { isValid: false, error: "Missing or invalid authorization header" }
    }

    const token = authHeader.substring(7)
    if (!token) {
      return { isValid: false, error: "Missing JWT token" }
    }

    // 在实际应用中，这里应该验证JWT token的有效性
    // 目前返回一个模拟的用户ID
    // TODO: 实现真正的JWT验证逻辑
    return { isValid: true, userId: "mock_user_id" }
  } catch (_error) {
    return { isValid: false, error: "Token verification failed" }
  }
}
