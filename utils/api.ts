/**
 * 统一的API调用工具
 * 提供统一的错误处理、JWT过期处理和请求拦截
 */

import { authenticatedFetch, authenticatedFetchJson } from "./auth"

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001"

/**
 * API响应的通用类型
 */
export interface ApiResponse<T = any> {
  code?: number
  data?: T
  msg?: string
  message?: string
  [key: string]: any
}

/**
 * API错误类
 */
export class ApiError extends Error {
  public status: number
  public code?: number

  constructor(message: string, status: number = 500, code?: number) {
    super(message)
    this.name = "ApiError"
    this.status = status
    this.code = code
  }
}

/**
 * 统一的API调用类
 */
export class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
  }

  /**
   * GET请求
   * @param endpoint API端点
   * @param params 查询参数
   * @returns Promise<T>
   */
  async get<T = any>(endpoint: string, params?: Record<string, string | number>): Promise<T> {
    let url = `${this.baseUrl}${endpoint}`

    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        searchParams.append(key, String(value))
      })
      url += `?${searchParams.toString()}`
    }

    const data = await authenticatedFetchJson(url, {
      method: "GET",
    })

    return data.data || data
  }

  /**
   * POST请求
   * @param endpoint API端点
   * @param body 请求体
   * @returns Promise<T>
   */
  async post<T = any>(endpoint: string, body?: any): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`

    const data = await authenticatedFetchJson(url, {
      method: "POST",
      body: body ? JSON.stringify(body) : undefined,
    })

    return data.data || data
  }

  /**
   * PUT请求
   * @param endpoint API端点
   * @param body 请求体
   * @returns Promise<T>
   */
  async put<T = any>(endpoint: string, body?: any): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`

    const data = await authenticatedFetchJson(url, {
      method: "PUT",
      body: body ? JSON.stringify(body) : undefined,
    })

    return data.data || data
  }

  /**
   * PATCH请求
   * @param endpoint API端点
   * @param body 请求体
   * @returns Promise<T>
   */
  async patch<T = any>(endpoint: string, body?: any): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`

    const data = await authenticatedFetchJson(url, {
      method: "PATCH",
      body: body ? JSON.stringify(body) : undefined,
    })

    return data.data || data
  }

  /**
   * DELETE请求
   * @param endpoint API端点
   * @returns Promise<T>
   */
  async delete<T = any>(endpoint: string): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`

    const data = await authenticatedFetchJson(url, {
      method: "DELETE",
    })

    return data.data || data
  }

  /**
   * 原始fetch请求（不自动解析JSON）
   * @param endpoint API端点
   * @param options fetch选项
   * @returns Promise<Response>
   */
  async raw(endpoint: string, options: RequestInit = {}): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`
    return authenticatedFetch(url, options)
  }
}

// 默认的API客户端实例
export const apiClient = new ApiClient()

// 便捷的导出函数
export const api = {
  get: <T = any>(endpoint: string, params?: Record<string, string | number>) => apiClient.get<T>(endpoint, params),

  post: <T = any>(endpoint: string, body?: any) => apiClient.post<T>(endpoint, body),

  put: <T = any>(endpoint: string, body?: any) => apiClient.put<T>(endpoint, body),

  patch: <T = any>(endpoint: string, body?: any) => apiClient.patch<T>(endpoint, body),

  delete: <T = any>(endpoint: string) => apiClient.delete<T>(endpoint),

  raw: (endpoint: string, options?: RequestInit) => apiClient.raw(endpoint, options),
}
