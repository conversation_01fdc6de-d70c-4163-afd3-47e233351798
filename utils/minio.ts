/**
 * MinIO 工具函数
 * 提供统一的MinIO资源URL生成
 */

const MINIO_BASE_URL = "https://minioapi.bugmaker.me/ifmb-2025-public"

/**
 * 获取首页背景图片URL
 */
export function getHomePageImageUrl(): string {
  return `${MINIO_BASE_URL}/home-page.jpg`
}

/**
 * 获取favicon URL
 */
export function getFaviconUrl(): string {
  return `${MINIO_BASE_URL}/favicon.ico`
}

/**
 * 获取图案SVG URL
 */
export function getPatternSvgUrl(): string {
  return `${MINIO_BASE_URL}/pattern.svg`
}

/**
 * 获取报告者图片URL
 * @param region 地区
 * @param filename 文件名
 */
export function getReporterImageUrl(region: string, filename: string): string {
  return `${MINIO_BASE_URL}/reporters/${filename}`
}

/**
 * 获取文档URL
 * @param filename 文件名
 */
export function getDocumentUrl(filename: string): string {
  return `${MINIO_BASE_URL}/docs/${filename}`
}
